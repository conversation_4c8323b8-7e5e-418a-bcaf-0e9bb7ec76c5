import 'package:flutter/material.dart';
import '../services/database_service.dart';

class DatabaseTestPage extends StatefulWidget {
  const DatabaseTestPage({super.key});

  @override
  State<DatabaseTestPage> createState() => _DatabaseTestPageState();
}

class _DatabaseTestPageState extends State<DatabaseTestPage> {
  final DatabaseService _dbService = DatabaseService();
  String _connectionStatus = 'Not connected';
  List<String> _tables = [];
  List<String> _logs = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _addLog('Database test page initialized');
  }

  void _addLog(String message) {
    setState(() {
      _logs.add('${DateTime.now().toString().substring(11, 19)}: $message');
    });
    print(message);
  }

  Future<void> _testConnection() async {
    setState(() {
      _isLoading = true;
      _connectionStatus = 'Connecting...';
    });

    _addLog('Testing database connection...');
    
    try {
      final success = await _dbService.testConnection();
      
      setState(() {
        _connectionStatus = success ? 'Connected successfully!' : 'Connection failed';
        _isLoading = false;
      });

      if (success) {
        _addLog('✅ Database connection successful!');
        await _loadTables();
      } else {
        _addLog('❌ Database connection failed');
      }
    } catch (e) {
      setState(() {
        _connectionStatus = 'Connection error: $e';
        _isLoading = false;
      });
      _addLog('❌ Connection error: $e');
    }
  }

  Future<void> _loadTables() async {
    _addLog('Loading database tables...');
    
    try {
      final tables = await _dbService.getTables();
      setState(() {
        _tables = tables;
      });
      
      if (tables.isNotEmpty) {
        _addLog('✅ Found ${tables.length} tables: ${tables.join(', ')}');
      } else {
        _addLog('⚠️ No tables found in database');
      }
    } catch (e) {
      _addLog('❌ Error loading tables: $e');
    }
  }

  Future<void> _disconnect() async {
    _addLog('Disconnecting from database...');
    
    try {
      await _dbService.disconnect();
      setState(() {
        _connectionStatus = 'Disconnected';
        _tables = [];
      });
      _addLog('✅ Disconnected successfully');
    } catch (e) {
      _addLog('❌ Error disconnecting: $e');
    }
  }

  void _clearLogs() {
    setState(() {
      _logs.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Database Connection Test'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Connection Info Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Database Connection Info',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text('Host: ************:3306'),
                    Text('Database: hyndaimoniyotr'),
                    Text('User: wy'),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        const Text('Status: '),
                        Text(
                          _connectionStatus,
                          style: TextStyle(
                            color: _connectionStatus.contains('success') 
                                ? Colors.green 
                                : _connectionStatus.contains('fail') || _connectionStatus.contains('error')
                                    ? Colors.red 
                                    : Colors.orange,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Action Buttons
            Row(
              children: [
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _testConnection,
                  icon: _isLoading 
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.wifi),
                  label: Text(_isLoading ? 'Connecting...' : 'Test Connection'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton.icon(
                  onPressed: _dbService.isConnected ? _disconnect : null,
                  icon: const Icon(Icons.wifi_off),
                  label: const Text('Disconnect'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton.icon(
                  onPressed: _clearLogs,
                  icon: const Icon(Icons.clear),
                  label: const Text('Clear Logs'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Tables Section
            if (_tables.isNotEmpty) ...[
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Database Tables (${_tables.length})',
                        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 8,
                        runSpacing: 4,
                        children: _tables.map((table) => Chip(
                          label: Text(table),
                          backgroundColor: Colors.blue[100],
                        )).toList(),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Logs Section
            const Text(
              'Connection Logs',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: _logs.map((log) => Padding(
                      padding: const EdgeInsets.symmetric(vertical: 2),
                      child: Text(
                        log,
                        style: const TextStyle(
                          fontFamily: 'monospace',
                          fontSize: 12,
                        ),
                      ),
                    )).toList(),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _dbService.disconnect();
    super.dispose();
  }
}
