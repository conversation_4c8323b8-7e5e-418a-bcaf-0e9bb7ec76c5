import 'package:flutter/material.dart';
import '../services/database_service.dart';
import '../services/torque_data_service.dart';

class DatabaseTestPage extends StatefulWidget {
  const DatabaseTestPage({super.key});

  @override
  State<DatabaseTestPage> createState() => _DatabaseTestPageState();
}

class _DatabaseTestPageState extends State<DatabaseTestPage> {
  final DatabaseService _dbService = DatabaseService();
  final TorqueDataService _torqueService = TorqueDataService();
  String _connectionStatus = 'Not connected';
  List<String> _tables = [];
  final List<String> _logs = [];
  bool _isLoading = false;
  int _torqueDataCount = 0;

  @override
  void initState() {
    super.initState();
    _addLog('Database test page initialized');
  }

  void _addLog(String message) {
    setState(() {
      _logs.add('${DateTime.now().toString().substring(11, 19)}: $message');
    });
    print(message);
  }

  Future<void> _testConnection() async {
    setState(() {
      _isLoading = true;
      _connectionStatus = 'Connecting...';
    });

    _addLog('Testing database connection...');
    
    try {
      final success = await _dbService.testConnection();
      
      setState(() {
        _connectionStatus = success ? 'Connected successfully!' : 'Connection failed';
        _isLoading = false;
      });

      if (success) {
        _addLog('✅ Database connection successful!');
        await _loadTables();
      } else {
        _addLog('❌ Database connection failed');
      }
    } catch (e) {
      setState(() {
        _connectionStatus = 'Connection error: $e';
        _isLoading = false;
      });
      _addLog('❌ Connection error: $e');
    }
  }

  Future<void> _loadTables() async {
    _addLog('Loading database tables...');
    
    try {
      final tables = await _dbService.getTables();
      setState(() {
        _tables = tables;
      });
      
      if (tables.isNotEmpty) {
        _addLog('✅ Found ${tables.length} tables: ${tables.join(', ')}');
      } else {
        _addLog('⚠️ No tables found in database');
      }
    } catch (e) {
      _addLog('❌ Error loading tables: $e');
    }
  }

  Future<void> _disconnect() async {
    _addLog('Disconnecting from database...');
    
    try {
      await _dbService.disconnect();
      setState(() {
        _connectionStatus = 'Disconnected';
        _tables = [];
      });
      _addLog('✅ Disconnected successfully');
    } catch (e) {
      _addLog('❌ Error disconnecting: $e');
    }
  }

  void _clearLogs() {
    setState(() {
      _logs.clear();
    });
  }

  Future<void> _createTorqueTable() async {
    _addLog('Creating torque_value_visulization table...');

    try {
      final success = await _torqueService.createTable();

      if (success) {
        _addLog('✅ Torque table created successfully!');
        await _loadTables(); // Refresh table list
      } else {
        _addLog('❌ Failed to create torque table');
      }
    } catch (e) {
      _addLog('❌ Error creating torque table: $e');
    }
  }

  Future<void> _insertSampleData() async {
    _addLog('Inserting sample torque data...');

    try {
      final success = await _torqueService.insertSampleData();

      if (success) {
        _addLog('✅ Sample torque data inserted successfully!');
        await _getTorqueDataCount();
      } else {
        _addLog('❌ Failed to insert sample data');
      }
    } catch (e) {
      _addLog('❌ Error inserting sample data: $e');
    }
  }

  Future<void> _getTorqueDataCount() async {
    _addLog('Getting torque data count...');

    try {
      final data = await _torqueService.getAllTorqueData();
      setState(() {
        _torqueDataCount = data.length;
      });
      _addLog('✅ Found ${data.length} torque data records');

      if (data.isNotEmpty) {
        final latest = data.first;
        _addLog('Latest record: VIN ${latest.vinNumber}, Progress ${latest.progressPercentage}%');
      }
    } catch (e) {
      _addLog('❌ Error getting torque data: $e');
    }
  }

  Future<void> _testTorqueOperations() async {
    _addLog('Testing complete torque data operations...');

    try {
      // 1. Create table
      await _createTorqueTable();

      // 2. Insert sample data
      await _insertSampleData();

      // 3. Get data count
      await _getTorqueDataCount();

      _addLog('✅ All torque operations completed successfully!');
    } catch (e) {
      _addLog('❌ Error in torque operations: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Database Connection Test'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Connection Info Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Database Connection Info',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text('Host: ************:3306'),
                    Text('Database: hyndaimoniyotr'),
                    Text('User: wy'),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        const Text('Status: '),
                        Text(
                          _connectionStatus,
                          style: TextStyle(
                            color: _connectionStatus.contains('success') 
                                ? Colors.green 
                                : _connectionStatus.contains('fail') || _connectionStatus.contains('error')
                                    ? Colors.red 
                                    : Colors.orange,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Action Buttons Row 1
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _testConnection,
                  icon: _isLoading
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.wifi),
                  label: Text(_isLoading ? 'Connecting...' : 'Test Connection'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _dbService.isConnected ? _disconnect : null,
                  icon: const Icon(Icons.wifi_off),
                  label: const Text('Disconnect'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _clearLogs,
                  icon: const Icon(Icons.clear),
                  label: const Text('Clear Logs'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 8),

            // Torque Table Buttons Row 2
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: _dbService.isConnected ? _createTorqueTable : null,
                  icon: const Icon(Icons.table_chart),
                  label: const Text('Create Torque Table'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.purple,
                    foregroundColor: Colors.white,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _dbService.isConnected ? _insertSampleData : null,
                  icon: const Icon(Icons.add_circle),
                  label: const Text('Insert Sample Data'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _dbService.isConnected ? _getTorqueDataCount : null,
                  icon: const Icon(Icons.analytics),
                  label: const Text('Get Data Count'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _dbService.isConnected ? _testTorqueOperations : null,
                  icon: const Icon(Icons.play_arrow),
                  label: const Text('Test All Operations'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.teal,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Tables Section
            if (_tables.isNotEmpty) ...[
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Database Tables (${_tables.length})',
                        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 8,
                        runSpacing: 4,
                        children: _tables.map((table) => Chip(
                          label: Text(table),
                          backgroundColor: table == 'torgue_value_visulization'
                              ? Colors.green[100]
                              : Colors.blue[100],
                          avatar: table == 'torgue_value_visulization'
                              ? const Icon(Icons.star, size: 16, color: Colors.green)
                              : null,
                        )).toList(),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Torque Data Section
            if (_torqueDataCount > 0) ...[
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(Icons.analytics, color: Colors.green),
                          const SizedBox(width: 8),
                          Text(
                            'Torque Data Records',
                            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.green[50],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.green[200]!),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.storage, color: Colors.green),
                            const SizedBox(width: 8),
                            Text(
                              'Total Records: $_torqueDataCount',
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: Colors.green,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'Table contains all monitoring values from the monitor screen:\n'
                        '• Vehicle Information (Model, VIN, SEQ)\n'
                        '• AT 监测值 (4 values + status)\n'
                        '• 当前监测值 (车身前) (1 value + status)\n'
                        '• 监测值 (2 values + status)\n'
                        '• 后轮监测结果 (2 values + status)\n'
                        '• Inspection Items (2 status)\n'
                        '• Progress Percentage',
                        style: TextStyle(fontSize: 12, color: Colors.grey),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Logs Section
            const Text(
              'Connection Logs',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: _logs.map((log) => Padding(
                      padding: const EdgeInsets.symmetric(vertical: 2),
                      child: Text(
                        log,
                        style: const TextStyle(
                          fontFamily: 'monospace',
                          fontSize: 12,
                        ),
                      ),
                    )).toList(),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _dbService.disconnect();
    super.dispose();
  }
}
