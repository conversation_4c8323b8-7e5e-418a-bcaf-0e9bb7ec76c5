# Generated code do not commit.
file(TO_CMAKE_PATH "F:\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "F:\\hyndaimonitorautomation" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=F:\\flutter"
  "PROJECT_DIR=F:\\hyndaimonitorautomation"
  "FLUTTER_ROOT=F:\\flutter"
  "FLUTTER_EPHEMERAL_DIR=F:\\hyndaimonitorautomation\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=F:\\hyndaimonitorautomation"
  "FLUTTER_TARGET=F:\\hyndaimonitorautomation\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=F:\\hyndaimonitorautomation\\.dart_tool\\package_config.json"
)
