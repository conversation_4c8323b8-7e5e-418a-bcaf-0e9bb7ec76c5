import 'package:flutter/material.dart';

class DataAnalysisScreen extends StatefulWidget {
  const DataAnalysisScreen({super.key});

  @override
  State<DataAnalysisScreen> createState() => _DataAnalysisScreenState();
}

class _DataAnalysisScreenState extends State<DataAnalysisScreen> {
  // Sample inspection data
  final List<Map<String, dynamic>> inspectionData = [
    {
      'id': '001',
      'item': '发动机检查',
      'category': '动力系统',
      'checkpoint': 'CP001',
      'standard': '正常运转',
      'result': 'OK',
      'value': '95%',
      'operator': '<PERSON>-jun',
      'date': '2024-01-29',
      'time': '09:30',
      'notes': '运转正常',
      'status': 'pass'
    },
    {
      'id': '002',
      'item': '制动系统检查',
      'category': '安全系统',
      'checkpoint': 'CP002',
      'standard': '制动距离<40m',
      'result': 'NG',
      'value': '45m',
      'operator': '<PERSON> So-young',
      'date': '2024-01-29',
      'time': '10:15',
      'notes': '制动距离超标',
      'status': 'fail'
    },
    {
      'id': '003',
      'item': '灯光系统检查',
      'category': '电气系统',
      'checkpoint': 'CP003',
      'standard': '亮度>1000lm',
      'result': 'OK',
      'value': '1250lm',
      'operator': 'Park Jae-ho',
      'date': '2024-01-29',
      'time': '11:00',
      'notes': '亮度符合标准',
      'status': 'pass'
    },
    {
      'id': '004',
      'item': '轮胎检查',
      'category': '底盘系统',
      'checkpoint': 'CP004',
      'standard': '花纹深度>1.6mm',
      'result': 'OK',
      'value': '2.1mm',
      'operator': 'Kim Min-jun',
      'date': '2024-01-29',
      'time': '11:30',
      'notes': '花纹深度正常',
      'status': 'pass'
    },
    {
      'id': '005',
      'item': '排放检查',
      'category': '环保系统',
      'checkpoint': 'CP005',
      'standard': 'CO<0.5%',
      'result': 'NG',
      'value': '0.7%',
      'operator': 'Lee So-young',
      'date': '2024-01-29',
      'time': '12:00',
      'notes': 'CO排放超标',
      'status': 'fail'
    },
  ];

  // Filter and sort options
  String selectedCategory = 'All';
  String selectedStatus = 'All';
  String sortBy = 'date';
  bool ascending = true;

  // Statistics
  int get totalItems => inspectionData.length;
  int get passedItems => inspectionData.where((item) => item['status'] == 'pass').length;
  int get failedItems => inspectionData.where((item) => item['status'] == 'fail').length;
  double get passRate => totalItems > 0 ? (passedItems / totalItems) * 100 : 0;

  List<String> get categories => ['All', ...inspectionData.map((item) => item['category']).toSet().toList()];

  List<Map<String, dynamic>> get filteredData {
    var filtered = inspectionData.where((item) {
      bool categoryMatch = selectedCategory == 'All' || item['category'] == selectedCategory;
      bool statusMatch = selectedStatus == 'All' || item['status'] == selectedStatus;
      return categoryMatch && statusMatch;
    }).toList();

    filtered.sort((a, b) {
      int comparison = 0;
      switch (sortBy) {
        case 'date':
          comparison = a['date'].compareTo(b['date']);
          break;
        case 'item':
          comparison = a['item'].compareTo(b['item']);
          break;
        case 'category':
          comparison = a['category'].compareTo(b['category']);
          break;
        case 'status':
          comparison = a['status'].compareTo(b['status']);
          break;
      }
      return ascending ? comparison : -comparison;
    });

    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          '检查数据分析管理系统',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
        elevation: 2,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              setState(() {
                // Refresh data
              });
            },
          ),
          IconButton(
            icon: const Icon(Icons.download),
            onPressed: () {
              // Export data
              _showExportDialog();
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Header with statistics
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.white,
            child: Column(
              children: [
                // Statistics Cards
                Row(
                  children: [
                    Expanded(child: _buildStatCard('总检查项目', totalItems.toString(), Colors.blue)),
                    const SizedBox(width: 8),
                    Expanded(child: _buildStatCard('通过项目', passedItems.toString(), Colors.green)),
                    const SizedBox(width: 8),
                    Expanded(child: _buildStatCard('失败项目', failedItems.toString(), Colors.red)),
                    const SizedBox(width: 8),
                    Expanded(child: _buildStatCard('通过率', '${passRate.toStringAsFixed(1)}%', Colors.orange)),
                  ],
                ),
                const SizedBox(height: 16),
                
                // Filters and Controls
                Row(
                  children: [
                    // Category Filter
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text('检查类别', style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold)),
                          DropdownButton<String>(
                            value: selectedCategory,
                            isExpanded: true,
                            items: categories.map((category) {
                              return DropdownMenuItem(value: category, child: Text(category));
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                selectedCategory = value!;
                              });
                            },
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 16),
                    
                    // Status Filter
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text('检查状态', style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold)),
                          DropdownButton<String>(
                            value: selectedStatus,
                            isExpanded: true,
                            items: const [
                              DropdownMenuItem(value: 'All', child: Text('全部')),
                              DropdownMenuItem(value: 'pass', child: Text('通过')),
                              DropdownMenuItem(value: 'fail', child: Text('失败')),
                            ],
                            onChanged: (value) {
                              setState(() {
                                selectedStatus = value!;
                              });
                            },
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 16),
                    
                    // Sort Options
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text('排序方式', style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold)),
                          Row(
                            children: [
                              Expanded(
                                child: DropdownButton<String>(
                                  value: sortBy,
                                  isExpanded: true,
                                  items: const [
                                    DropdownMenuItem(value: 'date', child: Text('日期')),
                                    DropdownMenuItem(value: 'item', child: Text('项目')),
                                    DropdownMenuItem(value: 'category', child: Text('类别')),
                                    DropdownMenuItem(value: 'status', child: Text('状态')),
                                  ],
                                  onChanged: (value) {
                                    setState(() {
                                      sortBy = value!;
                                    });
                                  },
                                ),
                              ),
                              IconButton(
                                icon: Icon(ascending ? Icons.arrow_upward : Icons.arrow_downward),
                                onPressed: () {
                                  setState(() {
                                    ascending = !ascending;
                                  });
                                },
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // Data Table
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.2),
                    spreadRadius: 1,
                    blurRadius: 3,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                children: [
                  // Table Header
                  Container(
                    padding: const EdgeInsets.all(12.0),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(8),
                        topRight: Radius.circular(8),
                      ),
                    ),
                    child: const Row(
                      children: [
                        Expanded(flex: 1, child: Text('ID', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12))),
                        Expanded(flex: 3, child: Text('检查项目', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12))),
                        Expanded(flex: 2, child: Text('类别', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12))),
                        Expanded(flex: 2, child: Text('检查点', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12))),
                        Expanded(flex: 2, child: Text('标准', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12))),
                        Expanded(flex: 1, child: Text('结果', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12))),
                        Expanded(flex: 2, child: Text('测量值', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12))),
                        Expanded(flex: 2, child: Text('操作员', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12))),
                        Expanded(flex: 2, child: Text('日期时间', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12))),
                        Expanded(flex: 3, child: Text('备注', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12))),
                        Expanded(flex: 1, child: Text('操作', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12))),
                      ],
                    ),
                  ),
                  
                  // Table Data
                  Expanded(
                    child: ListView.builder(
                      itemCount: filteredData.length,
                      itemBuilder: (context, index) {
                        final item = filteredData[index];
                        return Container(
                          padding: const EdgeInsets.all(12.0),
                          decoration: BoxDecoration(
                            border: Border(
                              bottom: BorderSide(color: Colors.grey[200]!),
                            ),
                            color: item['status'] == 'fail' ? Colors.red[50] : null,
                          ),
                          child: Row(
                            children: [
                              Expanded(flex: 1, child: Text(item['id'], style: const TextStyle(fontSize: 11))),
                              Expanded(flex: 3, child: Text(item['item'], style: const TextStyle(fontSize: 11))),
                              Expanded(flex: 2, child: Text(item['category'], style: const TextStyle(fontSize: 11))),
                              Expanded(flex: 2, child: Text(item['checkpoint'], style: const TextStyle(fontSize: 11))),
                              Expanded(flex: 2, child: Text(item['standard'], style: const TextStyle(fontSize: 11))),
                              Expanded(
                                flex: 1,
                                child: Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                  decoration: BoxDecoration(
                                    color: item['result'] == 'OK' ? Colors.green : Colors.red,
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: Text(
                                    item['result'],
                                    style: const TextStyle(color: Colors.white, fontSize: 10, fontWeight: FontWeight.bold),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ),
                              Expanded(flex: 2, child: Text(item['value'], style: const TextStyle(fontSize: 11))),
                              Expanded(flex: 2, child: Text(item['operator'], style: const TextStyle(fontSize: 11))),
                              Expanded(
                                flex: 2,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(item['date'], style: const TextStyle(fontSize: 10)),
                                    Text(item['time'], style: const TextStyle(fontSize: 10, color: Colors.grey)),
                                  ],
                                ),
                              ),
                              Expanded(flex: 3, child: Text(item['notes'], style: const TextStyle(fontSize: 11))),
                              Expanded(
                                flex: 1,
                                child: Row(
                                  children: [
                                    IconButton(
                                      icon: const Icon(Icons.edit, size: 16),
                                      onPressed: () => _editItem(item),
                                    ),
                                    IconButton(
                                      icon: const Icon(Icons.camera_alt, size: 16),
                                      onPressed: () => _takePhoto(item),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _addNewItem(),
        backgroundColor: Colors.blue[700],
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            title,
            style: TextStyle(fontSize: 12, color: color.withOpacity(0.8), fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(fontSize: 18, color: color, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  void _editItem(Map<String, dynamic> item) {
    // Show edit dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('编辑检查项目: ${item['item']}'),
        content: const Text('编辑功能开发中...'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  void _takePhoto(Map<String, dynamic> item) {
    // Show photo capture dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('为 ${item['item']} 拍照'),
        content: const Text('拍照功能开发中...'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  void _addNewItem() {
    // Show add new item dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('添加新检查项目'),
        content: const Text('添加功能开发中...'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  void _showExportDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('导出数据'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: Icon(Icons.table_chart),
              title: Text('导出为 Excel'),
              subtitle: Text('包含所有检查数据'),
            ),
            ListTile(
              leading: Icon(Icons.picture_as_pdf),
              title: Text('导出为 PDF'),
              subtitle: Text('生成检查报告'),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Export functionality
            },
            child: const Text('导出'),
          ),
        ],
      ),
    );
  }
}
