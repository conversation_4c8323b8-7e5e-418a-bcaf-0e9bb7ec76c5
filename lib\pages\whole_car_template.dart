import 'package:flutter/material.dart';

class WholeCarTemplatePage extends StatefulWidget {
  const WholeCarTemplatePage({super.key});

  @override
  State<WholeCarTemplatePage> createState() => _WholeCarTemplatePageState();
}

class _WholeCarTemplatePageState extends State<WholeCarTemplatePage> {
  // Vehicle Information
  final String vehicleModel = 'M6W';
  final String seqNumber = '003';
  final String vinNumber = 'LBEN2BKDBSZ050315';
  final String postLineVin = 'LBECNAFD2SZ477474';
  
  // DTC Status
  bool inspectionOk = true;
  bool inspectionNg = false;
  double progressPercentage = 75.0;

  // DTC Codes with their status
  final List<Map<String, dynamic>> dtcCodes = [
    {'code': 'P154', 'status': 'error', 'description': '燃料泵'},
    {'code': 'Q011', 'status': 'normal', 'description': '氧传感器'},
    {'code': 'Q012', 'status': 'normal', 'description': '氧传感器'},
    {'code': 'P020', 'status': 'warning', 'description': '进气歧管'},
    {'code': 'Q360', 'status': 'ok', 'description': '启动器'},
    {'code': 'P041', 'status': 'warning', 'description': '发动机主泵'},
    {'code': 'P042', 'status': 'warning', 'description': '发动机主泵'},
    {'code': 'Q047', 'status': 'warning', 'description': '天线'},
  ];

  Color _getStatusColor(String status) {
    switch (status) {
      case 'error':
        return Colors.red[100]!;
      case 'warning':
        return Colors.orange[100]!;
      case 'ok':
        return Colors.green[100]!;
      case 'normal':
      default:
        return Colors.grey[200]!;
    }
  }

  Color _getStatusTextColor(String status) {
    switch (status) {
      case 'error':
        return Colors.red[800]!;
      case 'warning':
        return Colors.orange[800]!;
      case 'ok':
        return Colors.green[800]!;
      case 'normal':
      default:
        return Colors.grey[800]!;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: AppBar(
        title: const Text(
          'T1 KEEPER - 整车模板',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
        elevation: 2,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Information
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[400]!, width: 2),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.2),
                    spreadRadius: 1,
                    blurRadius: 3,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                children: [
                  // Title Row
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.blue[100],
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: const Text(
                          '基本信息',
                          style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                        ),
                      ),
                      const SizedBox(width: 16),
                      const Text(
                        '2.0 DLX V2',
                        style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(width: 16),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.blue[100],
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: const Text('内饰色 NNB'),
                      ),
                      const SizedBox(width: 16),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.blue[100],
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: const Text('外部色 NKA'),
                      ),
                      const Spacer(),
                      const Text(
                        'T1 KEEPER',
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Main Content Row
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Left Side - DTC Codes
                      Expanded(
                        flex: 2,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              '整车诊断信息',
                              style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                            ),
                            const SizedBox(height: 12),
                            
                            // DTC Grid
                            GridView.builder(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount: 4,
                                childAspectRatio: 2.0,
                                crossAxisSpacing: 8,
                                mainAxisSpacing: 8,
                              ),
                              itemCount: dtcCodes.length,
                              itemBuilder: (context, index) {
                                final dtc = dtcCodes[index];
                                return Container(
                                  decoration: BoxDecoration(
                                    color: _getStatusColor(dtc['status']),
                                    borderRadius: BorderRadius.circular(6),
                                    border: Border.all(
                                      color: _getStatusTextColor(dtc['status']),
                                      width: 1,
                                    ),
                                  ),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        dtc['code'],
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                          color: _getStatusTextColor(dtc['status']),
                                        ),
                                      ),
                                      const SizedBox(height: 2),
                                      Text(
                                        dtc['description'],
                                        style: TextStyle(
                                          fontSize: 10,
                                          color: _getStatusTextColor(dtc['status']),
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ],
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                      
                      const SizedBox(width: 24),
                      
                      // Right Side - Vehicle Information
                      Expanded(
                        flex: 1,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              '车辆信息',
                              style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                            ),
                            const SizedBox(height: 12),
                            
                            // Vehicle Info Cards
                            _buildInfoCard('车型:', vehicleModel),
                            const SizedBox(height: 8),
                            _buildInfoCard('SEQ:', seqNumber),
                            const SizedBox(height: 8),
                            _buildInfoCard('VIN No:', ''),
                            const SizedBox(height: 4),
                            Container(
                              width: double.infinity,
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: Colors.grey[100],
                                borderRadius: BorderRadius.circular(4),
                                border: Border.all(color: Colors.grey[300]!),
                              ),
                              child: Text(
                                vinNumber,
                                style: const TextStyle(fontSize: 10, fontFamily: 'monospace'),
                              ),
                            ),
                            const SizedBox(height: 8),
                            _buildInfoCard('下辆车VIN NO:', ''),
                            const SizedBox(height: 4),
                            Container(
                              width: double.infinity,
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: Colors.grey[100],
                                borderRadius: BorderRadius.circular(4),
                                border: Border.all(color: Colors.grey[300]!),
                              ),
                              child: Text(
                                postLineVin,
                                style: const TextStyle(fontSize: 10, fontFamily: 'monospace'),
                              ),
                            ),
                            const SizedBox(height: 16),
                            
                            // Inspection Status
                            const Text(
                              '检查项目',
                              style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Container(
                                  width: 40,
                                  height: 24,
                                  decoration: BoxDecoration(
                                    color: inspectionOk ? Colors.green : Colors.grey[300],
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: const Center(
                                    child: Text(
                                      'OK',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 12,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Container(
                                  width: 40,
                                  height: 24,
                                  decoration: BoxDecoration(
                                    color: inspectionNg ? Colors.red : Colors.grey[300],
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: const Center(
                                    child: Text(
                                      'NG',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 12,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            
                            // Progress Bar
                            const Text(
                              '进度',
                              style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                            ),
                            const SizedBox(height: 8),
                            Container(
                              width: double.infinity,
                              height: 20,
                              decoration: BoxDecoration(
                                color: Colors.grey[300],
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: Stack(
                                children: [
                                  Container(
                                    width: double.infinity * (progressPercentage / 100),
                                    height: 20,
                                    decoration: BoxDecoration(
                                      color: Colors.green,
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                  ),
                                  Center(
                                    child: Text(
                                      '${progressPercentage.toInt()}%',
                                      style: const TextStyle(
                                        fontSize: 12,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.white,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Action Buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton.icon(
                  onPressed: () {
                    // Refresh DTC codes
                    setState(() {
                      // Simulate refresh
                    });
                  },
                  icon: const Icon(Icons.refresh),
                  label: const Text('刷新诊断'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () {
                    // Clear DTC codes
                    setState(() {
                      // Simulate clear
                    });
                  },
                  icon: const Icon(Icons.clear),
                  label: const Text('清除故障码'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () {
                    // Save report
                    setState(() {
                      // Simulate save
                    });
                  },
                  icon: const Icon(Icons.save),
                  label: const Text('保存报告'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 60,
          child: Text(
            label,
            style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(fontSize: 12),
          ),
        ),
      ],
    );
  }
}
