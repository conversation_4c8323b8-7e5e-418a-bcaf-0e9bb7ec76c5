import 'package:flutter/material.dart';

class WholeCarTemplatePage extends StatefulWidget {
  const WholeCarTemplatePage({super.key});

  @override
  State<WholeCarTemplatePage> createState() => _WholeCarTemplatePageState();
}

class _WholeCarTemplatePageState extends State<WholeCarTemplatePage> {
  // Vehicle Information
  final String vehicleModel = 'M6W';
  final String seqNumber = '003';
  final String vinNumber = 'LBEN2BKDBSZ050315';
  final String postLineVin = 'LBECNAFD2SZ477474';
  
  // DTC Status
  bool inspectionOk = true;
  bool inspectionNg = false;
  double progressPercentage = 75.0;

  // DTC Codes with their status
  final List<Map<String, dynamic>> dtcCodes = [
    {'code': 'P154', 'status': 'error', 'description': '燃料泵'},
    {'code': 'Q011', 'status': 'normal', 'description': '氧传感器'},
    {'code': 'Q012', 'status': 'normal', 'description': '氧传感器'},
    {'code': 'P020', 'status': 'warning', 'description': '进气歧管'},
    {'code': 'Q360', 'status': 'ok', 'description': '启动器'},
    {'code': 'P041', 'status': 'warning', 'description': '发动机主泵'},
    {'code': 'P042', 'status': 'warning', 'description': '发动机主泵'},
    {'code': 'Q047', 'status': 'warning', 'description': '天线'},
  ];

  Color _getStatusColor(String status) {
    switch (status) {
      case 'error':
        return Colors.red[100]!;
      case 'warning':
        return Colors.orange[100]!;
      case 'ok':
        return Colors.green[100]!;
      case 'normal':
      default:
        return Colors.grey[200]!;
    }
  }

  Color _getStatusTextColor(String status) {
    switch (status) {
      case 'error':
        return Colors.red[800]!;
      case 'warning':
        return Colors.orange[800]!;
      case 'ok':
        return Colors.green[800]!;
      case 'normal':
      default:
        return Colors.grey[800]!;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: AppBar(
        title: const Text('Whole Car Template'),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: Colors.grey[400]!, width: 2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              // Header
              _buildHeader(),

              // Main content
              Expanded(
                child: Row(
                  children: [
                    // Left panel - DTC diagnostic data
                    Expanded(
                      flex: 2,
                      child: _buildLeftPanel(),
                    ),

                    // Right panel - Vehicle info
                    Expanded(
                      flex: 1,
                      child: _buildRightPanel(),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey[200],
        border: Border(bottom: BorderSide(color: Colors.grey[400]!)),
      ),
      child: Row(
        children: [
          const Text(
            '基本信息',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(width: 20),
          _buildHeaderButton('2.0 DLX V2', Colors.blue),
          const SizedBox(width: 10),
          const Text('内饰色'),
          const SizedBox(width: 5),
          _buildHeaderButton('NNB', Colors.blue),
          const SizedBox(width: 10),
          const Text('外装色'),
          const SizedBox(width: 5),
          _buildHeaderButton('NKA', Colors.blue),
          const Spacer(),
          const Text(
            'TI KEEPER',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderButton(String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        text,
        style: const TextStyle(color: Colors.white, fontSize: 12),
      ),
    );
  }

  Widget _buildLeftPanel() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '整车诊断信息',
            style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
          ),
          const SizedBox(height: 16),

          // DTC Grid with proper table structure
          Row(
            children: [
              // Table 1: DTC Codes Row 1 (Left table)
              Expanded(child: _buildDTCTable1()),
              const SizedBox(width: 8),

              // Table 2: DTC Codes Row 2 (Center table)
              _buildDTCTable2(),
              const SizedBox(width: 8),

              // Table 3: DTC Codes Row 3 (Right table)
              Expanded(child: _buildDTCTable3()),
            ],
          ),

          const SizedBox(height: 16),

          // Table 4: DTC Codes Row 4 (Bottom table)
          _buildDTCTable4(),
        ],
      ),
    );
  }

  Widget _buildRightPanel() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(left: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInfoSection('车辆信息', [
            _buildInfoRow('车型:', vehicleModel),
            _buildInfoRow('SEQ:', seqNumber),
            _buildInfoRow('VIN No:', vinNumber),
            _buildInfoRow('下线后VIN No:', postLineVin),
          ]),

          const SizedBox(height: 20),

          _buildInfoSection('检查项目', [
            _buildStatusIndicator('OK', Colors.green),
            _buildStatusIndicator('NG', Colors.red),
          ]),

          const SizedBox(height: 20),

          _buildInfoSection('进度', [
            Container(
              height: 20,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(10),
              ),
              child: Row(
                children: [
                  Expanded(
                    flex: (progressPercentage / 10).round(), // Convert percentage to flex
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.green,
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 10 - (progressPercentage / 10).round(), // Remaining space
                    child: Container(),
                  ),
                ],
              ),
            ),
          ]),
        ],
      ),
    );
  }

  // DTC Table methods matching monitor_screen.dart structure
  Widget _buildDTCTable1() {
    return _buildTable([
      ['P154', '燃料泵', 'error'],
      ['Q011', '氧传感器', 'normal'],
    ]);
  }

  Widget _buildDTCTable2() {
    return _buildTable([
      ['Q012', '氧传感器', 'normal'],
      ['P020', '进气歧管', 'warning'],
    ]);
  }

  Widget _buildDTCTable3() {
    return _buildTable([
      ['Q360', '启动器', 'ok'],
      ['P041', '发动机主泵', 'warning'],
    ]);
  }

  Widget _buildDTCTable4() {
    return _buildTable([
      ['P042', '发动机主泵', 'warning'],
      ['Q047', '天线', 'warning'],
    ]);
  }

  Widget _buildTable(List<List<String>> data) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[400]!),
      ),
      child: Column(
        children: [
          // Header row
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.grey[200],
              border: Border(bottom: BorderSide(color: Colors.grey[400]!)),
            ),
            child: const Row(
              children: [
                Expanded(child: Text('DTC Code', style: TextStyle(fontWeight: FontWeight.bold))),
                Expanded(child: Text('Description', style: TextStyle(fontWeight: FontWeight.bold))),
                Expanded(child: Text('Status', style: TextStyle(fontWeight: FontWeight.bold))),
              ],
            ),
          ),
          // Data rows
          ...data.map((row) => Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
            ),
            child: Row(
              children: [
                Expanded(child: Text(row[0])),
                Expanded(child: Text(row[1])),
                Expanded(child: _buildStatusCell(row[2])),
              ],
            ),
          )),
        ],
      ),
    );
  }

  Widget _buildStatusCell(String status) {
    Color color;
    String text;
    switch (status) {
      case 'error':
        color = Colors.red;
        text = 'NG';
        break;
      case 'warning':
        color = Colors.orange;
        text = 'WARN';
        break;
      case 'ok':
        color = Colors.green;
        text = 'OK';
        break;
      default:
        color = Colors.grey;
        text = 'NORM';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        text,
        style: const TextStyle(color: Colors.white, fontSize: 10),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildInfoSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
        ),
        const SizedBox(height: 8),
        ...children,
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: const TextStyle(fontSize: 12),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusIndicator(String text, Color color) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 2),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        text,
        style: const TextStyle(color: Colors.white, fontSize: 12),
      ),
    );
  }
}
