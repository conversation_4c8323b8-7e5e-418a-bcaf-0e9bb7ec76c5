-- Basic Information Tables for Hyundai Monitor Automation System
-- These tables store fundamental data for vehicle monitoring operations

-- ===========================================
-- VEHICLE BASIC INFORMATION TABLE
-- ===========================================
CREATE TABLE IF NOT EXISTS `vehicle_basic_info` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  
  -- Vehicle Identification
  `vin_number` VARCHAR(50) NOT NULL UNIQUE COMMENT 'Vehicle Identification Number',
  `vehicle_model` VARCHAR(50) NOT NULL DEFAULT 'M6W' COMMENT 'Vehicle model (M6W, etc.)',
  `seq_number` VARCHAR(10) NOT NULL COMMENT 'Sequence number',
  `post_line_vin` VARCHAR(50) COMMENT 'Post-production line VIN',
  
  -- Vehicle Specifications
  `engine_type` VARCHAR(50) COMMENT 'Engine type specification',
  `transmission_type` VARCHAR(50) COMMENT 'Transmission type (AT, MT, CVT)',
  `fuel_type` ENUM('gasoline', 'diesel', 'hybrid', 'electric') DEFAULT 'gasoline' COMMENT 'Fuel type',
  `vehicle_year` YEAR COMMENT 'Manufacturing year',
  `color_code` VARCHAR(20) COMMENT 'Vehicle color code',
  `trim_level` VARCHAR(50) COMMENT 'Trim level (base, premium, etc.)',
  
  -- Production Information
  `production_date` DATE COMMENT 'Production date',
  `production_line` VARCHAR(50) DEFAULT 'Line A' COMMENT 'Production line identifier',
  `production_shift` VARCHAR(10) COMMENT 'Production shift (A, B, C)',
  `production_batch` VARCHAR(50) COMMENT 'Production batch number',
  
  -- Quality Control
  `quality_status` ENUM('pending', 'in_progress', 'passed', 'failed', 'rework') DEFAULT 'pending' COMMENT 'Quality control status',
  `inspection_level` ENUM('basic', 'standard', 'premium', 'full') DEFAULT 'standard' COMMENT 'Inspection level required',
  
  -- Timestamps
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  -- Indexes
  INDEX `idx_vin_number` (`vin_number`),
  INDEX `idx_vehicle_model` (`vehicle_model`),
  INDEX `idx_production_date` (`production_date`),
  INDEX `idx_production_line` (`production_line`),
  INDEX `idx_quality_status` (`quality_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='Basic vehicle information and specifications';

-- ===========================================
-- OPERATOR BASIC INFORMATION TABLE
-- ===========================================
CREATE TABLE IF NOT EXISTS `operator_basic_info` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  
  -- Operator Identification
  `operator_id` VARCHAR(50) NOT NULL UNIQUE COMMENT 'Unique operator identifier',
  `employee_number` VARCHAR(20) COMMENT 'Employee number',
  `operator_name` VARCHAR(100) NOT NULL COMMENT 'Full name of operator',
  `username` VARCHAR(50) UNIQUE COMMENT 'System username',
  
  -- Contact Information
  `email` VARCHAR(100) COMMENT 'Email address',
  `phone` VARCHAR(20) COMMENT 'Phone number',
  `department` VARCHAR(50) COMMENT 'Department name',
  
  -- Work Information
  `position` VARCHAR(50) COMMENT 'Job position/title',
  `shift_preference` VARCHAR(10) COMMENT 'Preferred shift (A, B, C)',
  `production_line_assigned` VARCHAR(50) COMMENT 'Assigned production line',
  `certification_level` ENUM('trainee', 'operator', 'senior', 'supervisor', 'manager') DEFAULT 'operator' COMMENT 'Certification level',
  
  -- Permissions and Access
  `access_level` ENUM('read', 'write', 'admin', 'supervisor') DEFAULT 'write' COMMENT 'System access level',
  `can_approve_tests` BOOLEAN DEFAULT FALSE COMMENT 'Can approve test results',
  `can_modify_settings` BOOLEAN DEFAULT FALSE COMMENT 'Can modify system settings',
  
  -- Status
  `status` ENUM('active', 'inactive', 'suspended', 'training') DEFAULT 'active' COMMENT 'Operator status',
  `hire_date` DATE COMMENT 'Date of hire',
  `last_login` TIMESTAMP NULL COMMENT 'Last system login',
  
  -- Timestamps
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  -- Indexes
  INDEX `idx_operator_id` (`operator_id`),
  INDEX `idx_employee_number` (`employee_number`),
  INDEX `idx_operator_name` (`operator_name`),
  INDEX `idx_department` (`department`),
  INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='Basic operator information and permissions';

-- ===========================================
-- TEST SESSION BASIC INFORMATION TABLE
-- ===========================================
CREATE TABLE IF NOT EXISTS `test_session_basic_info` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  
  -- Session Identification
  `session_id` VARCHAR(50) NOT NULL UNIQUE COMMENT 'Unique test session identifier',
  `session_name` VARCHAR(100) COMMENT 'Descriptive session name',
  `session_type` ENUM('routine', 'quality_check', 'rework', 'final_inspection', 'random_sample') DEFAULT 'routine' COMMENT 'Type of test session',
  
  -- Related Information
  `vin_number` VARCHAR(50) NOT NULL COMMENT 'Vehicle being tested',
  `operator_id` VARCHAR(50) NOT NULL COMMENT 'Primary operator',
  `supervisor_id` VARCHAR(50) COMMENT 'Supervising operator',
  
  -- Session Details
  `test_location` VARCHAR(100) DEFAULT 'Production Line A' COMMENT 'Test location',
  `production_line` VARCHAR(50) DEFAULT 'Line A' COMMENT 'Production line',
  `shift_number` VARCHAR(10) COMMENT 'Shift during test',
  `station_number` VARCHAR(20) COMMENT 'Test station number',
  
  -- Timing Information
  `session_start_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Session start time',
  `session_end_time` TIMESTAMP NULL COMMENT 'Session end time',
  `estimated_duration_minutes` INT DEFAULT 30 COMMENT 'Estimated duration in minutes',
  `actual_duration_minutes` INT COMMENT 'Actual duration in minutes',
  
  -- Status and Results
  `session_status` ENUM('pending', 'in_progress', 'completed', 'failed', 'cancelled', 'paused') DEFAULT 'pending' COMMENT 'Current session status',
  `overall_result` ENUM('pass', 'fail', 'pending', 'inconclusive') DEFAULT 'pending' COMMENT 'Overall test result',
  `priority_level` ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal' COMMENT 'Test priority',
  
  -- Quality Metrics
  `tests_planned` INT DEFAULT 0 COMMENT 'Number of tests planned',
  `tests_completed` INT DEFAULT 0 COMMENT 'Number of tests completed',
  `tests_passed` INT DEFAULT 0 COMMENT 'Number of tests passed',
  `tests_failed` INT DEFAULT 0 COMMENT 'Number of tests failed',
  `completion_percentage` DECIMAL(5,2) DEFAULT 0.00 COMMENT 'Completion percentage',
  
  -- Additional Information
  `notes` TEXT COMMENT 'Session notes and comments',
  `issues_encountered` TEXT COMMENT 'Issues or problems encountered',
  `corrective_actions` TEXT COMMENT 'Corrective actions taken',
  
  -- Timestamps
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  -- Foreign Key Constraints
  FOREIGN KEY (`vin_number`) REFERENCES `vehicle_basic_info`(`vin_number`) ON UPDATE CASCADE,
  FOREIGN KEY (`operator_id`) REFERENCES `operator_basic_info`(`operator_id`) ON UPDATE CASCADE,
  FOREIGN KEY (`supervisor_id`) REFERENCES `operator_basic_info`(`operator_id`) ON UPDATE CASCADE,
  
  -- Indexes
  INDEX `idx_session_id` (`session_id`),
  INDEX `idx_vin_number` (`vin_number`),
  INDEX `idx_operator_id` (`operator_id`),
  INDEX `idx_session_status` (`session_status`),
  INDEX `idx_session_start_time` (`session_start_time`),
  INDEX `idx_overall_result` (`overall_result`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='Basic test session information and tracking';

-- ===========================================
-- PRODUCTION LINE BASIC INFORMATION TABLE
-- ===========================================
CREATE TABLE IF NOT EXISTS `production_line_basic_info` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  
  -- Line Identification
  `line_id` VARCHAR(50) NOT NULL UNIQUE COMMENT 'Production line identifier',
  `line_name` VARCHAR(100) NOT NULL COMMENT 'Production line name',
  `line_type` ENUM('assembly', 'testing', 'quality_control', 'final_inspection') DEFAULT 'assembly' COMMENT 'Type of production line',
  
  -- Location and Capacity
  `facility_name` VARCHAR(100) COMMENT 'Facility/plant name',
  `building_section` VARCHAR(50) COMMENT 'Building section',
  `floor_level` VARCHAR(20) COMMENT 'Floor level',
  `max_capacity_per_hour` INT COMMENT 'Maximum vehicles per hour',
  `max_capacity_per_shift` INT COMMENT 'Maximum vehicles per shift',
  
  -- Operating Information
  `operating_status` ENUM('active', 'inactive', 'maintenance', 'setup') DEFAULT 'active' COMMENT 'Current operating status',
  `shift_schedule` JSON COMMENT 'Shift schedule information',
  `standard_cycle_time_minutes` DECIMAL(5,2) COMMENT 'Standard cycle time in minutes',
  
  -- Quality Standards
  `quality_standards` JSON COMMENT 'Quality standards and specifications',
  `inspection_points` JSON COMMENT 'Inspection points configuration',
  `tolerance_settings` JSON COMMENT 'Tolerance settings for measurements',
  
  -- Equipment Information
  `equipment_list` JSON COMMENT 'List of equipment on this line',
  `last_maintenance_date` DATE COMMENT 'Last maintenance date',
  `next_maintenance_due` DATE COMMENT 'Next maintenance due date',
  
  -- Timestamps
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  -- Indexes
  INDEX `idx_line_id` (`line_id`),
  INDEX `idx_line_name` (`line_name`),
  INDEX `idx_operating_status` (`operating_status`),
  INDEX `idx_facility_name` (`facility_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='Basic production line information and configuration';

-- ===========================================
-- INSERT SAMPLE DATA
-- ===========================================

-- Sample Vehicle Basic Information
INSERT INTO `vehicle_basic_info` (
  `vin_number`, `vehicle_model`, `seq_number`, `post_line_vin`,
  `engine_type`, `transmission_type`, `fuel_type`, `vehicle_year`, `color_code`, `trim_level`,
  `production_date`, `production_line`, `production_shift`, `production_batch`,
  `quality_status`, `inspection_level`
) VALUES
('LBEN2BKDBSZ050315', 'M6W', '003', 'LBECNAFD2SZ477474',
 '2.0L Turbo', 'AT', 'gasoline', 2024, 'WP9', 'Premium',
 '2024-01-29', 'Line A', 'A', 'BATCH_2024_001',
 'in_progress', 'standard'),

('LBEN2BKDBSZ050316', 'M6W', '004', 'LBECNAFD2SZ477475',
 '2.0L Turbo', 'AT', 'gasoline', 2024, 'TB9', 'Base',
 '2024-01-29', 'Line A', 'A', 'BATCH_2024_001',
 'pending', 'standard'),

('LBEN2BKDBSZ050317', 'M6W', '005', 'LBECNAFD2SZ477476',
 '2.5L Hybrid', 'CVT', 'hybrid', 2024, 'R2S', 'Premium',
 '2024-01-29', 'Line B', 'B', 'BATCH_2024_002',
 'passed', 'full');

-- Sample Operator Basic Information
INSERT INTO `operator_basic_info` (
  `operator_id`, `employee_number`, `operator_name`, `username`,
  `email`, `phone`, `department`, `position`, `shift_preference`,
  `production_line_assigned`, `certification_level`, `access_level`,
  `can_approve_tests`, `can_modify_settings`, `status`, `hire_date`
) VALUES
('OP001', 'EMP2024001', 'Kim Min-jun', 'kminjun',
 '<EMAIL>', '+82-10-1234-5678', 'Quality Control', 'Senior Inspector', 'A',
 'Line A', 'senior', 'admin', TRUE, TRUE, 'active', '2023-03-15'),

('OP002', 'EMP2024002', 'Lee So-young', 'lsoyoung',
 '<EMAIL>', '+82-10-2345-6789', 'Production', 'Test Operator', 'B',
 'Line A', 'operator', 'write', FALSE, FALSE, 'active', '2023-06-20'),

('OP003', 'EMP2024003', 'Park Jae-ho', 'pjaeho',
 '<EMAIL>', '+82-10-3456-7890', 'Quality Control', 'Supervisor', 'A',
 'Line B', 'supervisor', 'admin', TRUE, TRUE, 'active', '2022-01-10'),

('SYSTEM', 'SYS001', 'System User', 'system',
 '<EMAIL>', NULL, 'IT', 'System Account', NULL,
 NULL, 'manager', 'admin', TRUE, TRUE, 'active', '2024-01-01');

-- Sample Production Line Basic Information
INSERT INTO `production_line_basic_info` (
  `line_id`, `line_name`, `line_type`, `facility_name`, `building_section`, `floor_level`,
  `max_capacity_per_hour`, `max_capacity_per_shift`, `operating_status`,
  `standard_cycle_time_minutes`, `last_maintenance_date`, `next_maintenance_due`
) VALUES
('LINE_A', 'Production Line A', 'assembly', 'Hyundai Ulsan Plant 1', 'Building A', 'Floor 1',
 60, 480, 'active', 1.5, '2024-01-20', '2024-02-20'),

('LINE_B', 'Production Line B', 'testing', 'Hyundai Ulsan Plant 1', 'Building A', 'Floor 1',
 45, 360, 'active', 2.0, '2024-01-25', '2024-02-25'),

('QC_LINE_1', 'Quality Control Line 1', 'quality_control', 'Hyundai Ulsan Plant 1', 'Building B', 'Floor 2',
 30, 240, 'active', 3.0, '2024-01-15', '2024-02-15');

-- Sample Test Session Basic Information
INSERT INTO `test_session_basic_info` (
  `session_id`, `session_name`, `session_type`, `vin_number`, `operator_id`, `supervisor_id`,
  `test_location`, `production_line`, `shift_number`, `station_number`,
  `estimated_duration_minutes`, `session_status`, `overall_result`, `priority_level`,
  `tests_planned`, `tests_completed`, `tests_passed`, `tests_failed`, `completion_percentage`,
  `notes`
) VALUES
('SESSION_2024_001', 'M6W Standard Inspection', 'routine', 'LBEN2BKDBSZ050315', 'OP002', 'OP001',
 'Production Line A', 'Line A', 'A', 'STATION_A1',
 30, 'in_progress', 'pending', 'normal',
 8, 5, 4, 1, 62.50,
 'Standard monitoring test in progress. One minor issue detected in current monitoring.'),

('SESSION_2024_002', 'M6W Quality Check', 'quality_check', 'LBEN2BKDBSZ050316', 'OP001', 'OP003',
 'Quality Control Line 1', 'QC_LINE_1', 'A', 'STATION_QC1',
 45, 'pending', 'pending', 'high',
 10, 0, 0, 0, 0.00,
 'Scheduled quality check for premium trim vehicle.'),

('SESSION_2024_003', 'M6W Final Inspection', 'final_inspection', 'LBEN2BKDBSZ050317', 'OP003', NULL,
 'Quality Control Line 1', 'QC_LINE_1', 'B', 'STATION_QC2',
 60, 'completed', 'pass', 'normal',
 12, 12, 12, 0, 100.00,
 'Final inspection completed successfully. All tests passed.');

-- ===========================================
-- CREATE VIEWS FOR EASY DATA ACCESS
-- ===========================================

-- Vehicle Summary View
CREATE OR REPLACE VIEW `vehicle_summary` AS
SELECT
  v.vin_number,
  v.vehicle_model,
  v.seq_number,
  v.quality_status,
  v.production_line,
  v.production_date,
  COUNT(t.id) as total_sessions,
  SUM(CASE WHEN t.overall_result = 'pass' THEN 1 ELSE 0 END) as passed_sessions,
  SUM(CASE WHEN t.overall_result = 'fail' THEN 1 ELSE 0 END) as failed_sessions,
  MAX(t.session_start_time) as last_test_time
FROM `vehicle_basic_info` v
LEFT JOIN `test_session_basic_info` t ON v.vin_number = t.vin_number
GROUP BY v.vin_number, v.vehicle_model, v.seq_number, v.quality_status, v.production_line, v.production_date
ORDER BY v.production_date DESC, v.seq_number;

-- Operator Performance View
CREATE OR REPLACE VIEW `operator_performance` AS
SELECT
  o.operator_id,
  o.operator_name,
  o.department,
  o.certification_level,
  COUNT(t.id) as total_sessions,
  SUM(CASE WHEN t.overall_result = 'pass' THEN 1 ELSE 0 END) as successful_sessions,
  ROUND(AVG(t.completion_percentage), 2) as avg_completion_rate,
  ROUND(AVG(t.actual_duration_minutes), 2) as avg_duration_minutes
FROM `operator_basic_info` o
LEFT JOIN `test_session_basic_info` t ON o.operator_id = t.operator_id
WHERE o.status = 'active'
GROUP BY o.operator_id, o.operator_name, o.department, o.certification_level
ORDER BY successful_sessions DESC;

-- Daily Production Summary View
CREATE OR REPLACE VIEW `daily_production_summary` AS
SELECT
  DATE(t.session_start_time) as production_date,
  t.production_line,
  COUNT(*) as total_sessions,
  SUM(CASE WHEN t.overall_result = 'pass' THEN 1 ELSE 0 END) as passed_sessions,
  SUM(CASE WHEN t.overall_result = 'fail' THEN 1 ELSE 0 END) as failed_sessions,
  SUM(CASE WHEN t.session_status = 'in_progress' THEN 1 ELSE 0 END) as in_progress_sessions,
  ROUND(AVG(t.completion_percentage), 2) as avg_completion_rate,
  MIN(t.session_start_time) as first_session_time,
  MAX(t.session_start_time) as last_session_time
FROM `test_session_basic_info` t
WHERE t.session_start_time >= DATE_SUB(CURRENT_DATE, INTERVAL 30 DAY)
GROUP BY DATE(t.session_start_time), t.production_line
ORDER BY production_date DESC, t.production_line;
