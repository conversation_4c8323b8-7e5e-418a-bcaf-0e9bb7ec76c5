import 'package:flutter/material.dart';
import 'monitoring_table.dart';

class MonitoringDataComponent extends StatelessWidget {
  const MonitoringDataComponent({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '行驶数据结果',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 16),
        
        // Top row with three tables
        Row(
          children: [
            // Left table - AT 监测值
            Expanded(
              child: MonitoringTable(
                title: 'AT 监测值',
                titleColor: Colors.cyan,
                data: const [
                  MonitoringData(label: '#1', value: '0.9-1.4 kg/m', subValue: '1.3', status: MonitoringStatus.normal),
                  MonitoringData(label: '#2', value: '', subValue: '1.3', status: MonitoringStatus.normal),
                  MonitoringData(label: '#3', value: '', subValue: '1.2', status: MonitoringStatus.normal),
                  MonitoringData(label: '#4', value: '', subValue: '1.3', status: MonitoringStatus.normal),
                ],
                isHorizontal: true,
              ),
            ),
            
            const SizedBox(width: 12),
            
            // Center table - 当前监测值 (车身前)
            MonitoringTable(
              title: '当前监测值 (车身前)',
              titleColor: Colors.purple,
              data: const [
                MonitoringData(
                  label: '#1',
                  value: '2.7-3.3 kg/m',
                  subValue: '2.5',
                  status: MonitoringStatus.warning,
                ),
              ],
              isCenter: true,
              backgroundColor: Colors.orange[50],
            ),
            
            const SizedBox(width: 12),
            
            // Right table - 监测值
            Expanded(
              child: MonitoringTable(
                title: '监测值',
                titleColor: Colors.orange[300]!,
                data: const [
                  MonitoringData(label: '#1', value: '0.9-1.4 kg/m', subValue: '1.3', status: MonitoringStatus.normal),
                  MonitoringData(label: '#2', value: '', subValue: '1.3', status: MonitoringStatus.normal),
                ],
                isHorizontal: true,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 20),
        
        // Bottom table - 后轮监测结果计算结果
        MonitoringTable(
          title: '后轮监测结果计算结果',
          titleColor: Colors.pink[200]!,
          data: const [
            MonitoringData(label: '#1', value: '0.7-1.1 kg/m', subValue: '1.0', status: MonitoringStatus.normal),
            MonitoringData(label: '#2', value: '', subValue: '0.9', status: MonitoringStatus.normal),
          ],
          isHorizontal: true,
          showTitleBackground: true,
        ),
      ],
    );
  }
}
