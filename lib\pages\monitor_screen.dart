import 'package:flutter/material.dart';

class MonitorScreen extends StatefulWidget {
  const MonitorScreen({super.key});

  @override
  State<MonitorScreen> createState() => _MonitorScreenState();
}

class _MonitorScreenState extends State<MonitorScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: Colors.grey[400]!, width: 2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              // Header
              _buildHeader(),
              
              // Main content
              Expanded(
                child: Row(
                  children: [
                    // Left panel - Monitoring data
                    Expanded(
                      flex: 2,
                      child: _buildLeftPanel(),
                    ),
                    
                    // Right panel - Vehicle info
                    Expanded(
                      flex: 1,
                      child: _buildRightPanel(),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey[200],
        border: Border(bottom: BorderSide(color: Colors.grey[400]!)),
      ),
      child: Row(
        children: [
          const Text(
            '基本信息',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(width: 20),
          _buildHeaderButton('2.0 DLX V2', Colors.blue),
          const SizedBox(width: 10),
          const Text('内饰色'),
          const SizedBox(width: 5),
          _buildHeaderButton('NNB', Colors.blue),
          const SizedBox(width: 10),
          const Text('外装色'),
          const SizedBox(width: 5),
          _buildHeaderButton('NKA', Colors.blue),
          const Spacer(),
          const Text(
            'TI KEEPER',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderButton(String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        text,
        style: const TextStyle(color: Colors.white, fontSize: 12),
      ),
    );
  }

  Widget _buildLeftPanel() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '行驶数据结果',
            style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
          ),
          const SizedBox(height: 16),
          
          // Top monitoring section
          _buildMonitoringSection(),
          
          const SizedBox(height: 20),
          
          // Bottom monitoring section
          _buildBottomMonitoringSection(),
        ],
      ),
    );
  }

  Widget _buildMonitoringSection() {
    return Row(
      children: [
        // Left side monitoring boxes
        Expanded(
          child: Column(
            children: [
              Row(
                children: [
                  _buildMonitorBox('#1', '0.9-1.4 kg/m', Colors.green),
                  const SizedBox(width: 8),
                  _buildMonitorBox('#2', '1.3', Colors.green),
                  const SizedBox(width: 8),
                  _buildMonitorBox('#3', '1.2', Colors.green),
                  const SizedBox(width: 8),
                  _buildMonitorBox('#4', '1.3', Colors.green),
                ],
              ),
            ],
          ),
        ),
        
        const SizedBox(width: 20),
        
        // Center section with purple header
        Container(
          width: 120,
          child: Column(
            children: [
              Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(vertical: 8),
                decoration: const BoxDecoration(
                  color: Colors.purple,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(4),
                    topRight: Radius.circular(4),
                  ),
                ),
                child: const Text(
                  '当前监测值（车身前）',
                  textAlign: TextAlign.center,
                  style: TextStyle(color: Colors.white, fontSize: 10),
                ),
              ),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.orange[100],
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(4),
                    bottomRight: Radius.circular(4),
                  ),
                ),
                child: Column(
                  children: [
                    const Text('#1', style: TextStyle(fontWeight: FontWeight.bold)),
                    const Text('2.7-3.3 kg/m'),
                    const SizedBox(height: 8),
                    const Text('2.5', style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    Container(
                      width: 20,
                      height: 20,
                      decoration: const BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        
        const SizedBox(width: 20),
        
        // Right side monitoring boxes
        Expanded(
          child: Column(
            children: [
              Row(
                children: [
                  _buildMonitorBox('#1', '0.9-1.4 kg/m', Colors.green),
                  const SizedBox(width: 8),
                  _buildMonitorBox('#2', '1.3', Colors.green),
                  const SizedBox(width: 8),
                  _buildMonitorBox('#3', '1.3', Colors.green),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildBottomMonitoringSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.pink[100],
            borderRadius: BorderRadius.circular(4),
          ),
          child: const Text(
            '后轮监测结果计算结果',
            style: TextStyle(fontSize: 12),
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            _buildMonitorBox('#1', '0.7-1.1 kg/m', Colors.green),
            const SizedBox(width: 16),
            _buildMonitorBox('#2', '1.0', Colors.green),
            const SizedBox(width: 16),
            _buildMonitorBox('', '0.9', Colors.green),
          ],
        ),
      ],
    );
  }

  Widget _buildMonitorBox(String label, String value, Color indicatorColor) {
    return Container(
      width: 80,
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Column(
        children: [
          if (label.isNotEmpty)
            Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
            ),
          Text(
            value,
            style: const TextStyle(fontSize: 10),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: indicatorColor,
              shape: BoxShape.circle,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRightPanel() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(left: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInfoSection('车辆信息', [
            _buildInfoRow('车型:', 'M6W'),
            _buildInfoRow('SEQ:', '003'),
            _buildInfoRow('VIN No:', 'LBEN2BKDBSZ050315'),
            _buildInfoRow('下线后VIN No:', 'LBECNAFD2SZ477474'),
          ]),
          
          const SizedBox(height: 20),
          
          _buildInfoSection('检查项目', [
            _buildStatusIndicator('', Colors.red),
            _buildStatusIndicator('', Colors.red),
            _buildStatusIndicator('', Colors.green),
          ]),
          
          const SizedBox(height: 20),
          
          _buildInfoSection('进度', [
            Container(
              height: 20,
              decoration: BoxDecoration(
                color: Colors.green,
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          ]),
        ],
      ),
    );
  }

  Widget _buildInfoSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
        ),
        const SizedBox(height: 8),
        ...children,
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(fontSize: 12),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusIndicator(String label, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Container(
            width: 16,
            height: 16,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 8),
          Text(label, style: const TextStyle(fontSize: 12)),
        ],
      ),
    );
  }
}
