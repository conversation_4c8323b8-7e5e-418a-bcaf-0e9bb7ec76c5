import 'package:flutter/material.dart';
import '../components/monitoring_data_component.dart';

class MonitorScreen extends StatefulWidget {
  const MonitorScreen({super.key});

  @override
  State<MonitorScreen> createState() => _MonitorScreenState();
}

class _MonitorScreenState extends State<MonitorScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: AppBar(
        title: const Text('Monitoring Dashboard'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: Colors.grey[400]!, width: 2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              // Header
              _buildHeader(),
              
              // Main content
              Expanded(
                child: Row(
                  children: [
                    // Left panel - Monitoring data
                    Expanded(
                      flex: 2,
                      child: _buildLeftPanel(),
                    ),
                    
                    // Right panel - Vehicle info
                    Expanded(
                      flex: 1,
                      child: _buildRightPanel(),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey[200],
        border: Border(bottom: BorderSide(color: Colors.grey[400]!)),
      ),
      child: Row(
        children: [
          const Text(
            '基本信息',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(width: 20),
          _buildHeaderButton('2.0 DLX V2', Colors.blue),
          const SizedBox(width: 10),
          const Text('内饰色'),
          const SizedBox(width: 5),
          _buildHeaderButton('NNB', Colors.blue),
          const SizedBox(width: 10),
          const Text('外装色'),
          const SizedBox(width: 5),
          _buildHeaderButton('NKA', Colors.blue),
          const Spacer(),
          const Text(
            'TI KEEPER',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderButton(String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        text,
        style: const TextStyle(color: Colors.white, fontSize: 12),
      ),
    );
  }

  Widget _buildLeftPanel() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '行驶数据结果',
            style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
          ),
          const SizedBox(height: 16),

          // Four tables with proper table structure
          Row(
            children: [
              // Table 1: AT 监测值 (Left table)
              Expanded(child: _buildTable1()),
              const SizedBox(width: 8),

              // Table 2: 当前监测值 (Center table)
              _buildTable2(),
              const SizedBox(width: 8),

              // Table 3: 监测值 (Right table)
              Expanded(child: _buildTable3()),
            ],
          ),

          const SizedBox(height: 16),

          // Table 4: 后轮监测结果计算结果 (Bottom table)
          _buildTable4(),
        ],
      ),
    );
  }

  // Table 1: AT 监测值 (Left table - Cyan header)
  Widget _buildTable1() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[400]!),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Column(
        children: [
          // Header row
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 8),
            decoration: const BoxDecoration(
              color: Colors.cyan,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(4),
                topRight: Radius.circular(4),
              ),
            ),
            child: const Text(
              'AT 换挡杆',
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold),
            ),
          ),
          // Row of four cells (#1, #2, #3, #4)
          Container(
            padding: const EdgeInsets.symmetric(vertical: 4),
            decoration: BoxDecoration(
              border: Border(bottom: BorderSide(color: Colors.grey[300]!, width: 0.5)),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildTableCell('#1'),
                _buildTableCell('#2'),
                _buildTableCell('#3'),
                _buildTableCell('#4'),
              ],
            ),
          ),
          // Row of one cell (range)
          Container(
            padding: const EdgeInsets.symmetric(vertical: 4),
            decoration: BoxDecoration(
              border: Border(bottom: BorderSide(color: Colors.grey[300]!, width: 0.5)),
            ),
            child: const Text(
              '0.9-1.4 kg/m',
              style: TextStyle(fontSize: 10),
            ),
          ),
          // Row of four values
          Container(
            padding: const EdgeInsets.symmetric(vertical: 4),
            decoration: BoxDecoration(
              border: Border(bottom: BorderSide(color: Colors.grey[300]!, width: 0.5)),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildTableCell('1.3'),
                _buildTableCell('1.3'),
                _buildTableCell('1.2'),
                _buildTableCell('1.3'),
              ],
            ),
          ),
          // Row of four status indicators
          Container(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildStatusDot(Colors.green),
                _buildStatusDot(Colors.green),
                _buildStatusDot(Colors.green),
                _buildStatusDot(Colors.green),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Table 2: 当前监测值 (Center table - Purple header)
  Widget _buildTable2() {
    return Container(
      width: 120,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[400]!),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Column(
        children: [
          // Header row
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 8),
            decoration: const BoxDecoration(
              color: Colors.purple,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(4),
                topRight: Radius.circular(4),
              ),
            ),
            child: const Text(
              '电瓶负极 (车身侧)',
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.white, fontSize: 9, fontWeight: FontWeight.bold),
            ),
          ),
          // Content with orange background
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.orange[50],
            ),
            child: Column(
              children: [
                // Row with #1
                _buildTableCell('#1'),
                const SizedBox(height: 4),
                // Row with range
                const Text(
                  '2.7-3.3 kg/m',
                  style: TextStyle(fontSize: 10),
                ),
                const SizedBox(height: 8),
                // Row with large value
                const Text(
                  '2.5',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                // Row with status indicator
                _buildStatusDot(Colors.red),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Table 3: 监测值 (Right table - Orange header)
  Widget _buildTable3() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[400]!),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Column(
        children: [
          // Header row
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 8),
            decoration: BoxDecoration(
              color: Colors.orange[300],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(4),
                topRight: Radius.circular(4),
              ),
            ),
            child: const Text(
              '膨胀阀',
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold),
            ),
          ),
          // Row of two cells (#1, #2)
          Container(
            padding: const EdgeInsets.symmetric(vertical: 4),
            decoration: BoxDecoration(
              border: Border(bottom: BorderSide(color: Colors.grey[300]!, width: 0.5)),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildTableCell('#1'),
                _buildTableCell('#2'),
              ],
            ),
          ),
          // Row of one cell (range)
          Container(
            padding: const EdgeInsets.symmetric(vertical: 4),
            decoration: BoxDecoration(
              border: Border(bottom: BorderSide(color: Colors.grey[300]!, width: 0.5)),
            ),
            child: const Text(
              '0.9-1.4 kg/m',
              style: TextStyle(fontSize: 10),
            ),
          ),
          // Row of two values
          Container(
            padding: const EdgeInsets.symmetric(vertical: 4),
            decoration: BoxDecoration(
              border: Border(bottom: BorderSide(color: Colors.grey[300]!, width: 0.5)),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildTableCell('1.3'),
                _buildTableCell('1.3'),
              ],
            ),
          ),
          // Row of two status indicators
          Container(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildStatusDot(Colors.green),
                _buildStatusDot(Colors.green),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Table 4: 后轮监测结果计算结果 (Bottom table - Pink header)
  Widget _buildTable4() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with pink background
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.pink[200],
            borderRadius: BorderRadius.circular(4),
          ),
          child: const Text(
            'yu',
            style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
          ),
        ),
        const SizedBox(height: 8),
        // Table content
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[400]!),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Column(
            children: [
              // Row of two cells (#1, #2)
              Container(
                padding: const EdgeInsets.symmetric(vertical: 4),
                decoration: BoxDecoration(
                  border: Border(bottom: BorderSide(color: Colors.grey[300]!, width: 0.5)),
                ),
                child: Row(
                  children: [
                    Expanded(child: Center(child: _buildTableCell('#1'))),
                    Container(
                      width: 0.5,
                      height: 20,
                      color: Colors.grey[300],
                    ),
                    Expanded(child: Center(child: _buildTableCell('#2'))),
                  ],
                ),
              ),
              // Row of one cell (range)
              Container(
                padding: const EdgeInsets.symmetric(vertical: 4),
                decoration: BoxDecoration(
                  border: Border(bottom: BorderSide(color: Colors.grey[300]!, width: 0.5)),
                ),
                child: const Text(
                  '0.7-1.1 kg/m',
                  style: TextStyle(fontSize: 10),
                ),
              ),
              // Row of two values
              Container(
                padding: const EdgeInsets.symmetric(vertical: 4),
                decoration: BoxDecoration(
                  border: Border(bottom: BorderSide(color: Colors.grey[300]!, width: 0.5)),
                ),
                child: Row(
                  children: [
                    Expanded(child: Center(child: _buildTableCell('1.0'))),
                    Container(
                      width: 0.5,
                      height: 20,
                      color: Colors.grey[300],
                    ),
                    Expanded(child: Center(child: _buildTableCell('0.9'))),
                  ],
                ),
              ),
              // Row of two status indicators
              Container(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: Row(
                  children: [
                    Expanded(child: Center(child: _buildStatusDot(Colors.green))),
                    Container(
                      width: 0.5,
                      height: 20,
                      color: Colors.grey[300],
                    ),
                    Expanded(child: Center(child: _buildStatusDot(Colors.green))),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Helper method to build table cells
  Widget _buildTableCell(String text) {
    return Text(
      text,
      style: const TextStyle(
        fontSize: 11,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  // Helper method to build status dots
  Widget _buildStatusDot(Color color) {
    return Container(
      width: 12,
      height: 12,
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
      ),
    );
  }

  Widget _buildSimpleDataBox(String label, String value, String subValue) {
    return Container(
      width: 60,
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (label.isNotEmpty)
            Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 10),
            ),
          if (value.isNotEmpty)
            Text(
              value,
              style: const TextStyle(fontSize: 8),
              textAlign: TextAlign.center,
            ),
          Text(
            subValue,
            style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 2),
          Container(
            width: 10,
            height: 10,
            decoration: const BoxDecoration(
              color: Colors.green,
              shape: BoxShape.circle,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRightPanel() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(left: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInfoSection('车辆信息', [
            _buildInfoRow('车型:', 'M6W'),
            _buildInfoRow('SEQ:', '003'),
            _buildInfoRow('VIN No:', 'LBEN2BKDBSZ050315'),
            _buildInfoRow('下线后VIN No:', 'LBECNAFD2SZ477474'),
          ]),
          
          const SizedBox(height: 20),
          
          _buildInfoSection('检查项目', [
            _buildStatusIndicator('OK', Colors.green),
            _buildStatusIndicator('NG', Colors.red),
          ]),
          
          const SizedBox(height: 20),
          
          _buildInfoSection('进度', [
            Container(
              height: 20,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(10),
              ),
              child: Row(
                children: [
                  Expanded(
                    flex: 6, // 60% filled
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.green,
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 4, // 40% empty
                    child: Container(),
                  ),
                ],
              ),
            ),
          ]),
        ],
      ),
    );
  }

  Widget _buildInfoSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
        ),
        const SizedBox(height: 8),
        ...children,
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(fontSize: 12),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusIndicator(String label, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Container(
            width: 16,
            height: 16,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 8),
          Text(label, style: const TextStyle(fontSize: 12)),
        ],
      ),
    );
  }
}
