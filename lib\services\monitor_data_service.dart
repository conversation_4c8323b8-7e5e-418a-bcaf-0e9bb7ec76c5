import 'package:mysql1/mysql1.dart';
import 'database_service.dart';

class MonitorScreenData {
  final int? id;
  
  // Vehicle Information
  final String vehicleModel;
  final String seqNumber;
  final String vinNumber;
  final String postLineVin;
  
  // Table 1: AT 监测值 (Cyan header)
  final String atTableTitle;
  final String atRangeSpec;
  final double atValue1;
  final double atValue2;
  final double atValue3;
  final double atValue4;
  final String atStatus1;
  final String atStatus2;
  final String atStatus3;
  final String atStatus4;
  
  // Table 2: 当前监测值 (车身前) (Purple header)
  final String currentTableTitle;
  final String currentRangeSpec;
  final double currentValue;
  final String currentStatus;
  final String currentSensorId;
  
  // Table 3: 监测值 (Orange header)
  final String monitorTableTitle;
  final String monitorRangeSpec;
  final double monitorValue1;
  final double monitorValue2;
  final String monitorStatus1;
  final String monitorStatus2;
  final String monitorSensor1;
  final String monitorSensor2;
  
  // Table 4: 后轮监测结果计算结果 (Pink header)
  final String rearWheelTableTitle;
  final String rearWheelRangeSpec;
  final double rearWheelValue1;
  final double rearWheelValue2;
  final String rearWheelStatus1;
  final String rearWheelStatus2;
  final String rearWheelSensor1;
  final String rearWheelSensor2;
  
  // Inspection Items
  final String inspectionItem1Label;
  final String inspectionItem1Status;
  final String inspectionItem2Label;
  final String inspectionItem2Status;
  
  // Progress
  final String progressLabel;
  final int progressPercentage;
  final int progressCompleted;
  final int progressRemaining;
  
  // Metadata
  final String? testSessionId;
  final String? operatorName;
  final String? testLocation;
  final String? productionLine;
  final String? notes;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  MonitorScreenData({
    this.id,
    required this.vehicleModel,
    required this.seqNumber,
    required this.vinNumber,
    required this.postLineVin,
    required this.atTableTitle,
    required this.atRangeSpec,
    required this.atValue1,
    required this.atValue2,
    required this.atValue3,
    required this.atValue4,
    required this.atStatus1,
    required this.atStatus2,
    required this.atStatus3,
    required this.atStatus4,
    required this.currentTableTitle,
    required this.currentRangeSpec,
    required this.currentValue,
    required this.currentStatus,
    required this.currentSensorId,
    required this.monitorTableTitle,
    required this.monitorRangeSpec,
    required this.monitorValue1,
    required this.monitorValue2,
    required this.monitorStatus1,
    required this.monitorStatus2,
    required this.monitorSensor1,
    required this.monitorSensor2,
    required this.rearWheelTableTitle,
    required this.rearWheelRangeSpec,
    required this.rearWheelValue1,
    required this.rearWheelValue2,
    required this.rearWheelStatus1,
    required this.rearWheelStatus2,
    required this.rearWheelSensor1,
    required this.rearWheelSensor2,
    required this.inspectionItem1Label,
    required this.inspectionItem1Status,
    required this.inspectionItem2Label,
    required this.inspectionItem2Status,
    required this.progressLabel,
    required this.progressPercentage,
    required this.progressCompleted,
    required this.progressRemaining,
    this.testSessionId,
    this.operatorName,
    this.testLocation,
    this.productionLine,
    this.notes,
    this.createdAt,
    this.updatedAt,
  });

  // Create from database row
  factory MonitorScreenData.fromRow(ResultRow row) {
    return MonitorScreenData(
      id: row['id'],
      vehicleModel: row['vehicle_model'] ?? '',
      seqNumber: row['seq_number'] ?? '',
      vinNumber: row['vin_number'] ?? '',
      postLineVin: row['post_line_vin'] ?? '',
      atTableTitle: row['at_table_title'] ?? '',
      atRangeSpec: row['at_range_spec'] ?? '',
      atValue1: (row['at_value_1'] ?? 0.0).toDouble(),
      atValue2: (row['at_value_2'] ?? 0.0).toDouble(),
      atValue3: (row['at_value_3'] ?? 0.0).toDouble(),
      atValue4: (row['at_value_4'] ?? 0.0).toDouble(),
      atStatus1: row['at_status_1'] ?? 'ok',
      atStatus2: row['at_status_2'] ?? 'ok',
      atStatus3: row['at_status_3'] ?? 'ok',
      atStatus4: row['at_status_4'] ?? 'ok',
      currentTableTitle: row['current_table_title'] ?? '',
      currentRangeSpec: row['current_range_spec'] ?? '',
      currentValue: (row['current_value'] ?? 0.0).toDouble(),
      currentStatus: row['current_status'] ?? 'ok',
      currentSensorId: row['current_sensor_id'] ?? '',
      monitorTableTitle: row['monitor_table_title'] ?? '',
      monitorRangeSpec: row['monitor_range_spec'] ?? '',
      monitorValue1: (row['monitor_value_1'] ?? 0.0).toDouble(),
      monitorValue2: (row['monitor_value_2'] ?? 0.0).toDouble(),
      monitorStatus1: row['monitor_status_1'] ?? 'ok',
      monitorStatus2: row['monitor_status_2'] ?? 'ok',
      monitorSensor1: row['monitor_sensor_1'] ?? '',
      monitorSensor2: row['monitor_sensor_2'] ?? '',
      rearWheelTableTitle: row['rear_wheel_table_title'] ?? '',
      rearWheelRangeSpec: row['rear_wheel_range_spec'] ?? '',
      rearWheelValue1: (row['rear_wheel_value_1'] ?? 0.0).toDouble(),
      rearWheelValue2: (row['rear_wheel_value_2'] ?? 0.0).toDouble(),
      rearWheelStatus1: row['rear_wheel_status_1'] ?? 'ok',
      rearWheelStatus2: row['rear_wheel_status_2'] ?? 'ok',
      rearWheelSensor1: row['rear_wheel_sensor_1'] ?? '',
      rearWheelSensor2: row['rear_wheel_sensor_2'] ?? '',
      inspectionItem1Label: row['inspection_item_1_label'] ?? '',
      inspectionItem1Status: row['inspection_item_1_status'] ?? 'ok',
      inspectionItem2Label: row['inspection_item_2_label'] ?? '',
      inspectionItem2Status: row['inspection_item_2_status'] ?? 'ng',
      progressLabel: row['progress_label'] ?? '',
      progressPercentage: row['progress_percentage'] ?? 0,
      progressCompleted: row['progress_completed'] ?? 0,
      progressRemaining: row['progress_remaining'] ?? 0,
      testSessionId: row['test_session_id'],
      operatorName: row['operator_name'],
      testLocation: row['test_location'],
      productionLine: row['production_line'],
      notes: row['notes'],
      createdAt: row['created_at'],
      updatedAt: row['updated_at'],
    );
  }

  // Convert to map for database insertion
  Map<String, dynamic> toMap() {
    return {
      'vehicle_model': vehicleModel,
      'seq_number': seqNumber,
      'vin_number': vinNumber,
      'post_line_vin': postLineVin,
      'at_table_title': atTableTitle,
      'at_range_spec': atRangeSpec,
      'at_value_1': atValue1,
      'at_value_2': atValue2,
      'at_value_3': atValue3,
      'at_value_4': atValue4,
      'at_status_1': atStatus1,
      'at_status_2': atStatus2,
      'at_status_3': atStatus3,
      'at_status_4': atStatus4,
      'current_table_title': currentTableTitle,
      'current_range_spec': currentRangeSpec,
      'current_value': currentValue,
      'current_status': currentStatus,
      'current_sensor_id': currentSensorId,
      'monitor_table_title': monitorTableTitle,
      'monitor_range_spec': monitorRangeSpec,
      'monitor_value_1': monitorValue1,
      'monitor_value_2': monitorValue2,
      'monitor_status_1': monitorStatus1,
      'monitor_status_2': monitorStatus2,
      'monitor_sensor_1': monitorSensor1,
      'monitor_sensor_2': monitorSensor2,
      'rear_wheel_table_title': rearWheelTableTitle,
      'rear_wheel_range_spec': rearWheelRangeSpec,
      'rear_wheel_value_1': rearWheelValue1,
      'rear_wheel_value_2': rearWheelValue2,
      'rear_wheel_status_1': rearWheelStatus1,
      'rear_wheel_status_2': rearWheelStatus2,
      'rear_wheel_sensor_1': rearWheelSensor1,
      'rear_wheel_sensor_2': rearWheelSensor2,
      'inspection_item_1_label': inspectionItem1Label,
      'inspection_item_1_status': inspectionItem1Status,
      'inspection_item_2_label': inspectionItem2Label,
      'inspection_item_2_status': inspectionItem2Status,
      'progress_label': progressLabel,
      'progress_percentage': progressPercentage,
      'progress_completed': progressCompleted,
      'progress_remaining': progressRemaining,
      'test_session_id': testSessionId,
      'operator_name': operatorName,
      'test_location': testLocation,
      'production_line': productionLine,
      'notes': notes,
    };
  }

  // Create default data matching current monitor screen
  factory MonitorScreenData.defaultData() {
    return MonitorScreenData(
      vehicleModel: 'M6W',
      seqNumber: '003',
      vinNumber: 'LBEN2BKDBSZ050315',
      postLineVin: 'LBECNAFD2SZ477474',
      atTableTitle: 'AT 监测值',
      atRangeSpec: '0.9-1.4 kg/m',
      atValue1: 1.30,
      atValue2: 1.30,
      atValue3: 1.20,
      atValue4: 1.30,
      atStatus1: 'ok',
      atStatus2: 'ok',
      atStatus3: 'ok',
      atStatus4: 'ok',
      currentTableTitle: '当前监测值 (车身前)',
      currentRangeSpec: '2.7-3.3 kg/m',
      currentValue: 2.50,
      currentStatus: 'ng',
      currentSensorId: '#1',
      monitorTableTitle: '监测值',
      monitorRangeSpec: '0.9-1.4 kg/m',
      monitorValue1: 1.30,
      monitorValue2: 1.30,
      monitorStatus1: 'ok',
      monitorStatus2: 'ok',
      monitorSensor1: '#1',
      monitorSensor2: '#2',
      rearWheelTableTitle: '后轮监测结果计算结果',
      rearWheelRangeSpec: '0.7-1.1 kg/m',
      rearWheelValue1: 1.00,
      rearWheelValue2: 0.90,
      rearWheelStatus1: 'ok',
      rearWheelStatus2: 'ok',
      rearWheelSensor1: '#1',
      rearWheelSensor2: '#2',
      inspectionItem1Label: 'OK',
      inspectionItem1Status: 'ok',
      inspectionItem2Label: 'NG',
      inspectionItem2Status: 'ng',
      progressLabel: '进度',
      progressPercentage: 60,
      progressCompleted: 60,
      progressRemaining: 40,
      testSessionId: 'SESSION_${DateTime.now().millisecondsSinceEpoch}',
      operatorName: 'System User',
      testLocation: 'Production Line A',
      productionLine: 'Line A',
      notes: 'Auto-generated from monitor screen',
    );
  }
}

class MonitorDataService {
  final DatabaseService _dbService = DatabaseService();

  // Create the monitor_screen_data table
  Future<bool> createMonitorTable() async {
    try {
      const sql = '''
        CREATE TABLE IF NOT EXISTS `monitor_screen_data` (
          `id` INT AUTO_INCREMENT PRIMARY KEY,
          `vehicle_model` VARCHAR(50) NOT NULL DEFAULT 'M6W',
          `seq_number` VARCHAR(10) NOT NULL DEFAULT '003',
          `vin_number` VARCHAR(50) NOT NULL DEFAULT 'LBEN2BKDBSZ050315',
          `post_line_vin` VARCHAR(50) NOT NULL DEFAULT 'LBECNAFD2SZ477474',
          `at_table_title` VARCHAR(50) DEFAULT 'AT 监测值',
          `at_range_spec` VARCHAR(30) DEFAULT '0.9-1.4 kg/m',
          `at_value_1` DECIMAL(4,2) DEFAULT 1.30,
          `at_value_2` DECIMAL(4,2) DEFAULT 1.30,
          `at_value_3` DECIMAL(4,2) DEFAULT 1.20,
          `at_value_4` DECIMAL(4,2) DEFAULT 1.30,
          `at_status_1` ENUM('ok', 'ng') DEFAULT 'ok',
          `at_status_2` ENUM('ok', 'ng') DEFAULT 'ok',
          `at_status_3` ENUM('ok', 'ng') DEFAULT 'ok',
          `at_status_4` ENUM('ok', 'ng') DEFAULT 'ok',
          `current_table_title` VARCHAR(50) DEFAULT '当前监测值 (车身前)',
          `current_range_spec` VARCHAR(30) DEFAULT '2.7-3.3 kg/m',
          `current_value` DECIMAL(4,2) DEFAULT 2.50,
          `current_status` ENUM('ok', 'ng') DEFAULT 'ng',
          `current_sensor_id` VARCHAR(10) DEFAULT '#1',
          `monitor_table_title` VARCHAR(50) DEFAULT '监测值',
          `monitor_range_spec` VARCHAR(30) DEFAULT '0.9-1.4 kg/m',
          `monitor_value_1` DECIMAL(4,2) DEFAULT 1.30,
          `monitor_value_2` DECIMAL(4,2) DEFAULT 1.30,
          `monitor_status_1` ENUM('ok', 'ng') DEFAULT 'ok',
          `monitor_status_2` ENUM('ok', 'ng') DEFAULT 'ok',
          `monitor_sensor_1` VARCHAR(10) DEFAULT '#1',
          `monitor_sensor_2` VARCHAR(10) DEFAULT '#2',
          `rear_wheel_table_title` VARCHAR(50) DEFAULT '后轮监测结果计算结果',
          `rear_wheel_range_spec` VARCHAR(30) DEFAULT '0.7-1.1 kg/m',
          `rear_wheel_value_1` DECIMAL(4,2) DEFAULT 1.00,
          `rear_wheel_value_2` DECIMAL(4,2) DEFAULT 0.90,
          `rear_wheel_status_1` ENUM('ok', 'ng') DEFAULT 'ok',
          `rear_wheel_status_2` ENUM('ok', 'ng') DEFAULT 'ok',
          `rear_wheel_sensor_1` VARCHAR(10) DEFAULT '#1',
          `rear_wheel_sensor_2` VARCHAR(10) DEFAULT '#2',
          `inspection_item_1_label` VARCHAR(20) DEFAULT 'OK',
          `inspection_item_1_status` ENUM('ok', 'ng') DEFAULT 'ok',
          `inspection_item_2_label` VARCHAR(20) DEFAULT 'NG',
          `inspection_item_2_status` ENUM('ok', 'ng') DEFAULT 'ng',
          `progress_label` VARCHAR(20) DEFAULT '进度',
          `progress_percentage` INT DEFAULT 60,
          `progress_completed` INT DEFAULT 60,
          `progress_remaining` INT DEFAULT 40,
          `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          `test_session_id` VARCHAR(50),
          `operator_name` VARCHAR(100),
          `test_location` VARCHAR(100),
          `production_line` VARCHAR(50),
          `notes` TEXT,
          INDEX `idx_created_at` (`created_at`),
          INDEX `idx_vin_number` (`vin_number`),
          INDEX `idx_test_session` (`test_session_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      ''';

      final result = await _dbService.query(sql);
      print('✅ Table monitor_screen_data created successfully');
      return result != null;
    } catch (e) {
      print('❌ Error creating monitor table: $e');
      return false;
    }
  }

  // Insert monitor screen data
  Future<int?> insertMonitorData(MonitorScreenData data) async {
    try {
      final id = await _dbService.insert('monitor_screen_data', data.toMap());
      if (id != null) {
        print('✅ Monitor data inserted with ID: $id');
      }
      return id;
    } catch (e) {
      print('❌ Error inserting monitor data: $e');
      return null;
    }
  }

  // Get all monitor data
  Future<List<MonitorScreenData>> getAllMonitorData() async {
    try {
      final results = await _dbService.query(
        'SELECT * FROM monitor_screen_data ORDER BY created_at DESC'
      );
      
      if (results == null) return [];

      List<MonitorScreenData> data = [];
      for (var row in results) {
        data.add(MonitorScreenData.fromRow(row));
      }
      
      print('✅ Retrieved ${data.length} monitor data records');
      return data;
    } catch (e) {
      print('❌ Error getting monitor data: $e');
      return [];
    }
  }

  // Insert sample data
  Future<bool> insertSampleMonitorData() async {
    try {
      final sampleData = MonitorScreenData.defaultData();
      final id = await insertMonitorData(sampleData);
      return id != null;
    } catch (e) {
      print('❌ Error inserting sample monitor data: $e');
      return false;
    }
  }
}
