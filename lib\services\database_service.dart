import 'package:mysql1/mysql1.dart';
import 'dart:io';

class DatabaseService {
  static const String _host = '************';
  static const int _port = 3306;
  static const String _user = 'wy';
  static const String _password = 'nolose';
  static const String _database = 'hyndaimoniyotr';

  MySqlConnection? _connection;

  // Singleton pattern
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  // Get connection settings
  ConnectionSettings get _connectionSettings => ConnectionSettings(
        host: _host,
        port: _port,
        user: _user,
        password: _password,
        db: _database,
        timeout: const Duration(seconds: 30),
      );

  // Connect to database
  Future<bool> connect() async {
    try {
      print('🔄 Attempting to connect to MySQL database...');
      print('📍 Host: $_host:$_port');
      print('👤 User: $_user');
      print('🗄️ Database: $_database');
      print('⏱️ Timeout: 30 seconds');

      _connection = await MySqlConnection.connect(_connectionSettings);
      print('✅ Successfully connected to MySQL database!');
      return true;
    } catch (e) {
      print('❌ Failed to connect to database: $e');
      print('🔍 Error type: ${e.runtimeType}');

      // Provide specific error guidance
      if (e.toString().contains('Connection refused')) {
        print('💡 Suggestion: Check if MySQL server is running on $_host:$_port');
      } else if (e.toString().contains('Access denied')) {
        print('💡 Suggestion: Check username/password credentials');
      } else if (e.toString().contains('Unknown database')) {
        print('💡 Suggestion: Check if database "$_database" exists');
      } else if (e.toString().contains('timeout')) {
        print('💡 Suggestion: Check network connectivity and firewall settings');
      }

      return false;
    }
  }

  // Disconnect from database
  Future<void> disconnect() async {
    try {
      await _connection?.close();
      _connection = null;
      print('Disconnected from database');
    } catch (e) {
      print('Error disconnecting from database: $e');
    }
  }

  // Check if connected
  bool get isConnected => _connection != null;

  // Test network connectivity to host
  Future<bool> testNetworkConnectivity() async {
    try {
      print('🌐 Testing network connectivity to $_host:$_port...');

      final socket = await Socket.connect(_host, _port, timeout: const Duration(seconds: 10));
      await socket.close();

      print('✅ Network connectivity successful!');
      return true;
    } catch (e) {
      print('❌ Network connectivity failed: $e');

      if (e.toString().contains('Connection timed out')) {
        print('💡 The host is not reachable or port is blocked');
      } else if (e.toString().contains('Connection refused')) {
        print('💡 Host is reachable but MySQL service is not running on port $_port');
      } else if (e.toString().contains('No route to host')) {
        print('💡 Network routing issue or host is down');
      }

      return false;
    }
  }

  // Test connection
  Future<bool> testConnection() async {
    try {
      if (!isConnected) {
        await connect();
      }
      
      final results = await _connection!.query('SELECT 1 as test');
      print('Database test query successful: ${results.first['test']}');
      return true;
    } catch (e) {
      print('Database test failed: $e');
      return false;
    }
  }

  // Get all tables in the database
  Future<List<String>> getTables() async {
    try {
      if (!isConnected) {
        await connect();
      }

      final results = await _connection!.query('SHOW TABLES');
      List<String> tables = [];
      
      for (var row in results) {
        tables.add(row[0].toString());
      }
      
      print('Found ${tables.length} tables: $tables');
      return tables;
    } catch (e) {
      print('Error getting tables: $e');
      return [];
    }
  }

  // Execute a query
  Future<Results?> query(String sql, [List<Object?>? values]) async {
    try {
      if (!isConnected) {
        await connect();
      }

      print('Executing query: $sql');
      if (values != null && values.isNotEmpty) {
        print('With values: $values');
        return await _connection!.query(sql, values);
      } else {
        return await _connection!.query(sql);
      }
    } catch (e) {
      print('Error executing query: $e');
      return null;
    }
  }

  // Insert data
  Future<int?> insert(String table, Map<String, dynamic> data) async {
    try {
      if (!isConnected) {
        await connect();
      }

      final columns = data.keys.join(', ');
      final placeholders = data.keys.map((e) => '?').join(', ');
      final values = data.values.toList();

      final sql = 'INSERT INTO $table ($columns) VALUES ($placeholders)';
      print('Inserting into $table: $data');
      
      final result = await _connection!.query(sql, values);
      print('Insert successful, ID: ${result.insertId}');
      return result.insertId;
    } catch (e) {
      print('Error inserting data: $e');
      return null;
    }
  }

  // Update data
  Future<bool> update(String table, Map<String, dynamic> data, String whereClause, [List<Object?>? whereValues]) async {
    try {
      if (!isConnected) {
        await connect();
      }

      final setClause = data.keys.map((key) => '$key = ?').join(', ');
      final values = [...data.values, ...?whereValues];

      final sql = 'UPDATE $table SET $setClause WHERE $whereClause';
      print('Updating $table: $data where $whereClause');
      
      final result = await _connection!.query(sql, values);
      print('Update successful, affected rows: ${result.affectedRows}');
      return result.affectedRows! > 0;
    } catch (e) {
      print('Error updating data: $e');
      return false;
    }
  }

  // Delete data
  Future<bool> delete(String table, String whereClause, [List<Object?>? whereValues]) async {
    try {
      if (!isConnected) {
        await connect();
      }

      final sql = 'DELETE FROM $table WHERE $whereClause';
      print('Deleting from $table where $whereClause');
      
      final result = await _connection!.query(sql, whereValues);
      print('Delete successful, affected rows: ${result.affectedRows}');
      return result.affectedRows! > 0;
    } catch (e) {
      print('Error deleting data: $e');
      return false;
    }
  }

  // Get monitoring data (example method)
  Future<List<Map<String, dynamic>>> getMonitoringData() async {
    try {
      final results = await query('SELECT * FROM monitoring_data ORDER BY created_at DESC LIMIT 10');
      if (results == null) return [];

      List<Map<String, dynamic>> data = [];
      for (var row in results) {
        data.add(row.fields);
      }
      return data;
    } catch (e) {
      print('Error getting monitoring data: $e');
      return [];
    }
  }

  // Save monitoring data (example method)
  Future<bool> saveMonitoringData(Map<String, dynamic> data) async {
    try {
      final id = await insert('monitoring_data', {
        ...data,
        'created_at': DateTime.now(),
        'updated_at': DateTime.now(),
      });
      return id != null;
    } catch (e) {
      print('Error saving monitoring data: $e');
      return false;
    }
  }
}
