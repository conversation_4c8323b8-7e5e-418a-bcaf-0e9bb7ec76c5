import 'package:flutter/material.dart';

enum MonitoringStatus {
  normal,
  warning,
  error,
}

class MonitoringData {
  final String label;
  final String value;
  final String subValue;
  final MonitoringStatus status;

  const MonitoringData({
    required this.label,
    required this.value,
    required this.subValue,
    required this.status,
  });
}

class MonitoringTable extends StatelessWidget {
  final String title;
  final Color titleColor;
  final List<MonitoringData> data;
  final bool isHorizontal;
  final bool isCenter;
  final bool showTitleBackground;
  final Color? backgroundColor;

  const MonitoringTable({
    super.key,
    required this.title,
    required this.titleColor,
    required this.data,
    this.isHorizontal = false,
    this.isCenter = false,
    this.showTitleBackground = false,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    if (isCenter) {
      return _buildCenterTable();
    }
    
    if (showTitleBackground) {
      return _buildTableWithTitleBackground();
    }
    
    return _buildRegularTable();
  }

  Widget _buildCenterTable() {
    final item = data.first;
    return Container(
      width: 140,
      child: Column(
        children: [
          // Title header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
            decoration: BoxDecoration(
              color: titleColor,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(4),
                topRight: Radius.circular(4),
              ),
            ),
            child: Text(
              title,
              textAlign: TextAlign.center,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          
          // Content
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: backgroundColor ?? Colors.orange[50],
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(4),
                bottomRight: Radius.circular(4),
              ),
            ),
            child: Column(
              children: [
                Text(
                  item.label,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  item.value,
                  style: const TextStyle(fontSize: 10),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  item.subValue,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                _buildStatusIndicator(item.status),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTableWithTitleBackground() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: titleColor,
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        const SizedBox(height: 8),
        _buildDataGrid(),
      ],
    );
  }

  Widget _buildRegularTable() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Column(
        children: [
          // Title header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 8),
            decoration: BoxDecoration(
              color: titleColor,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(4),
                topRight: Radius.circular(4),
              ),
            ),
            child: Text(
              title,
              textAlign: TextAlign.center,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 11,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          
          // Content
          Container(
            padding: const EdgeInsets.all(8),
            child: _buildDataGrid(),
          ),
        ],
      ),
    );
  }

  Widget _buildDataGrid() {
    if (isHorizontal) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: data.map((item) => _buildDataItem(item)).toList(),
      );
    } else {
      return Column(
        children: data.map((item) => Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: _buildDataItem(item),
        )).toList(),
      );
    }
  }

  Widget _buildDataItem(MonitoringData item) {
    return Container(
      width: isHorizontal ? 60 : null,
      child: Column(
        children: [
          if (item.label.isNotEmpty)
            Text(
              item.label,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 11,
              ),
            ),
          if (item.value.isNotEmpty) ...[
            const SizedBox(height: 2),
            Text(
              item.value,
              style: const TextStyle(fontSize: 9),
              textAlign: TextAlign.center,
            ),
          ],
          const SizedBox(height: 4),
          Text(
            item.subValue,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          _buildStatusIndicator(item.status),
        ],
      ),
    );
  }

  Widget _buildStatusIndicator(MonitoringStatus status) {
    Color color;
    switch (status) {
      case MonitoringStatus.normal:
        color = Colors.green;
        break;
      case MonitoringStatus.warning:
        color = Colors.red;
        break;
      case MonitoringStatus.error:
        color = Colors.red;
        break;
    }

    return Container(
      width: 12,
      height: 12,
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
      ),
    );
  }
}
