import 'package:mysql1/mysql1.dart';
import 'database_service.dart';

class TorqueValueData {
  final int? id;
  
  // Vehicle Information
  final String vehicleModel;
  final String seqNumber;
  final String vinNumber;
  final String postLineVin;
  
  // AT 监测值 (Table 1)
  final String atMonitorRange;
  final double atMonitor1;
  final double atMonitor2;
  final double atMonitor3;
  final double atMonitor4;
  final String atStatus1;
  final String atStatus2;
  final String atStatus3;
  final String atStatus4;
  
  // 当前监测值 (车身前) (Table 2)
  final String currentMonitorRange;
  final double currentMonitorValue;
  final String currentMonitorStatus;
  
  // 监测值 (Table 3)
  final String monitorRange;
  final double monitor1;
  final double monitor2;
  final String monitorStatus1;
  final String monitorStatus2;
  
  // 后轮监测结果计算结果 (Table 4)
  final String rearWheelRange;
  final double rearWheel1;
  final double rearWheel2;
  final String rearWheelStatus1;
  final String rearWheelStatus2;
  
  // Inspection Items
  final String inspectionStatus1;
  final String inspectionStatus2;
  
  // Progress
  final int progressPercentage;
  
  // Metadata
  final String? testSessionId;
  final String? operatorName;
  final String? testLocation;
  final String? notes;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  TorqueValueData({
    this.id,
    required this.vehicleModel,
    required this.seqNumber,
    required this.vinNumber,
    required this.postLineVin,
    required this.atMonitorRange,
    required this.atMonitor1,
    required this.atMonitor2,
    required this.atMonitor3,
    required this.atMonitor4,
    required this.atStatus1,
    required this.atStatus2,
    required this.atStatus3,
    required this.atStatus4,
    required this.currentMonitorRange,
    required this.currentMonitorValue,
    required this.currentMonitorStatus,
    required this.monitorRange,
    required this.monitor1,
    required this.monitor2,
    required this.monitorStatus1,
    required this.monitorStatus2,
    required this.rearWheelRange,
    required this.rearWheel1,
    required this.rearWheel2,
    required this.rearWheelStatus1,
    required this.rearWheelStatus2,
    required this.inspectionStatus1,
    required this.inspectionStatus2,
    required this.progressPercentage,
    this.testSessionId,
    this.operatorName,
    this.testLocation,
    this.notes,
    this.createdAt,
    this.updatedAt,
  });

  // Create from database row
  factory TorqueValueData.fromRow(ResultRow row) {
    return TorqueValueData(
      id: row['id'],
      vehicleModel: row['vehicle_model'] ?? '',
      seqNumber: row['seq_number'] ?? '',
      vinNumber: row['vin_number'] ?? '',
      postLineVin: row['post_line_vin'] ?? '',
      atMonitorRange: row['at_monitor_range'] ?? '',
      atMonitor1: (row['at_monitor_1'] ?? 0.0).toDouble(),
      atMonitor2: (row['at_monitor_2'] ?? 0.0).toDouble(),
      atMonitor3: (row['at_monitor_3'] ?? 0.0).toDouble(),
      atMonitor4: (row['at_monitor_4'] ?? 0.0).toDouble(),
      atStatus1: row['at_status_1'] ?? 'ok',
      atStatus2: row['at_status_2'] ?? 'ok',
      atStatus3: row['at_status_3'] ?? 'ok',
      atStatus4: row['at_status_4'] ?? 'ok',
      currentMonitorRange: row['current_monitor_range'] ?? '',
      currentMonitorValue: (row['current_monitor_value'] ?? 0.0).toDouble(),
      currentMonitorStatus: row['current_monitor_status'] ?? 'ok',
      monitorRange: row['monitor_range'] ?? '',
      monitor1: (row['monitor_1'] ?? 0.0).toDouble(),
      monitor2: (row['monitor_2'] ?? 0.0).toDouble(),
      monitorStatus1: row['monitor_status_1'] ?? 'ok',
      monitorStatus2: row['monitor_status_2'] ?? 'ok',
      rearWheelRange: row['rear_wheel_range'] ?? '',
      rearWheel1: (row['rear_wheel_1'] ?? 0.0).toDouble(),
      rearWheel2: (row['rear_wheel_2'] ?? 0.0).toDouble(),
      rearWheelStatus1: row['rear_wheel_status_1'] ?? 'ok',
      rearWheelStatus2: row['rear_wheel_status_2'] ?? 'ok',
      inspectionStatus1: row['inspection_status_1'] ?? 'ok',
      inspectionStatus2: row['inspection_status_2'] ?? 'ng',
      progressPercentage: row['progress_percentage'] ?? 0,
      testSessionId: row['test_session_id'],
      operatorName: row['operator_name'],
      testLocation: row['test_location'],
      notes: row['notes'],
      createdAt: row['created_at'],
      updatedAt: row['updated_at'],
    );
  }

  // Convert to map for database insertion
  Map<String, dynamic> toMap() {
    return {
      'vehicle_model': vehicleModel,
      'seq_number': seqNumber,
      'vin_number': vinNumber,
      'post_line_vin': postLineVin,
      'at_monitor_range': atMonitorRange,
      'at_monitor_1': atMonitor1,
      'at_monitor_2': atMonitor2,
      'at_monitor_3': atMonitor3,
      'at_monitor_4': atMonitor4,
      'at_status_1': atStatus1,
      'at_status_2': atStatus2,
      'at_status_3': atStatus3,
      'at_status_4': atStatus4,
      'current_monitor_range': currentMonitorRange,
      'current_monitor_value': currentMonitorValue,
      'current_monitor_status': currentMonitorStatus,
      'monitor_range': monitorRange,
      'monitor_1': monitor1,
      'monitor_2': monitor2,
      'monitor_status_1': monitorStatus1,
      'monitor_status_2': monitorStatus2,
      'rear_wheel_range': rearWheelRange,
      'rear_wheel_1': rearWheel1,
      'rear_wheel_2': rearWheel2,
      'rear_wheel_status_1': rearWheelStatus1,
      'rear_wheel_status_2': rearWheelStatus2,
      'inspection_status_1': inspectionStatus1,
      'inspection_status_2': inspectionStatus2,
      'progress_percentage': progressPercentage,
      'test_session_id': testSessionId,
      'operator_name': operatorName,
      'test_location': testLocation,
      'notes': notes,
    };
  }

  // Create default data matching current monitor screen
  factory TorqueValueData.defaultData() {
    return TorqueValueData(
      vehicleModel: 'M6W',
      seqNumber: '003',
      vinNumber: 'LBEN2BKDBSZ050315',
      postLineVin: 'LBECNAFD2SZ477474',
      atMonitorRange: '0.9-1.4 kg/m',
      atMonitor1: 1.3,
      atMonitor2: 1.3,
      atMonitor3: 1.2,
      atMonitor4: 1.3,
      atStatus1: 'ok',
      atStatus2: 'ok',
      atStatus3: 'ok',
      atStatus4: 'ok',
      currentMonitorRange: '2.7-3.3 kg/m',
      currentMonitorValue: 2.5,
      currentMonitorStatus: 'ng',
      monitorRange: '0.9-1.4 kg/m',
      monitor1: 1.3,
      monitor2: 1.3,
      monitorStatus1: 'ok',
      monitorStatus2: 'ok',
      rearWheelRange: '0.7-1.1 kg/m',
      rearWheel1: 1.0,
      rearWheel2: 0.9,
      rearWheelStatus1: 'ok',
      rearWheelStatus2: 'ok',
      inspectionStatus1: 'ok',
      inspectionStatus2: 'ng',
      progressPercentage: 60,
      testSessionId: 'SESSION_${DateTime.now().millisecondsSinceEpoch}',
      operatorName: 'System User',
      testLocation: 'Production Line A',
      notes: 'Auto-generated from monitor screen',
    );
  }
}

class TorqueDataService {
  final DatabaseService _dbService = DatabaseService();

  // Create the table if it doesn't exist
  Future<bool> createTable() async {
    try {
      const sql = '''
        CREATE TABLE IF NOT EXISTS `torgue_value_visulization` (
          `id` INT AUTO_INCREMENT PRIMARY KEY,
          `vehicle_model` VARCHAR(50) DEFAULT 'M6W',
          `seq_number` VARCHAR(10) DEFAULT '003',
          `vin_number` VARCHAR(50) DEFAULT 'LBEN2BKDBSZ050315',
          `post_line_vin` VARCHAR(50) DEFAULT 'LBECNAFD2SZ477474',
          `at_monitor_range` VARCHAR(20) DEFAULT '0.9-1.4 kg/m',
          `at_monitor_1` DECIMAL(3,1) DEFAULT 1.3,
          `at_monitor_2` DECIMAL(3,1) DEFAULT 1.3,
          `at_monitor_3` DECIMAL(3,1) DEFAULT 1.2,
          `at_monitor_4` DECIMAL(3,1) DEFAULT 1.3,
          `at_status_1` ENUM('ok', 'ng') DEFAULT 'ok',
          `at_status_2` ENUM('ok', 'ng') DEFAULT 'ok',
          `at_status_3` ENUM('ok', 'ng') DEFAULT 'ok',
          `at_status_4` ENUM('ok', 'ng') DEFAULT 'ok',
          `current_monitor_range` VARCHAR(20) DEFAULT '2.7-3.3 kg/m',
          `current_monitor_value` DECIMAL(3,1) DEFAULT 2.5,
          `current_monitor_status` ENUM('ok', 'ng') DEFAULT 'ng',
          `monitor_range` VARCHAR(20) DEFAULT '0.9-1.4 kg/m',
          `monitor_1` DECIMAL(3,1) DEFAULT 1.3,
          `monitor_2` DECIMAL(3,1) DEFAULT 1.3,
          `monitor_status_1` ENUM('ok', 'ng') DEFAULT 'ok',
          `monitor_status_2` ENUM('ok', 'ng') DEFAULT 'ok',
          `rear_wheel_range` VARCHAR(20) DEFAULT '0.7-1.1 kg/m',
          `rear_wheel_1` DECIMAL(3,1) DEFAULT 1.0,
          `rear_wheel_2` DECIMAL(3,1) DEFAULT 0.9,
          `rear_wheel_status_1` ENUM('ok', 'ng') DEFAULT 'ok',
          `rear_wheel_status_2` ENUM('ok', 'ng') DEFAULT 'ok',
          `inspection_status_1` ENUM('ok', 'ng') DEFAULT 'ok',
          `inspection_status_2` ENUM('ok', 'ng') DEFAULT 'ng',
          `progress_percentage` INT DEFAULT 60,
          `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          `test_session_id` VARCHAR(50),
          `operator_name` VARCHAR(100),
          `test_location` VARCHAR(100),
          `notes` TEXT,
          INDEX `idx_created_at` (`created_at`),
          INDEX `idx_vin_number` (`vin_number`),
          INDEX `idx_test_session` (`test_session_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      ''';

      final result = await _dbService.query(sql);
      print('✅ Table torgue_value_visulization created successfully');
      return result != null;
    } catch (e) {
      print('❌ Error creating table: $e');
      return false;
    }
  }

  // Insert torque data
  Future<int?> insertTorqueData(TorqueValueData data) async {
    try {
      final id = await _dbService.insert('torgue_value_visulization', data.toMap());
      if (id != null) {
        print('✅ Torque data inserted with ID: $id');
      }
      return id;
    } catch (e) {
      print('❌ Error inserting torque data: $e');
      return null;
    }
  }

  // Get all torque data
  Future<List<TorqueValueData>> getAllTorqueData() async {
    try {
      final results = await _dbService.query(
        'SELECT * FROM torgue_value_visulization ORDER BY created_at DESC'
      );
      
      if (results == null) return [];

      List<TorqueValueData> data = [];
      for (var row in results) {
        data.add(TorqueValueData.fromRow(row));
      }
      
      print('✅ Retrieved ${data.length} torque data records');
      return data;
    } catch (e) {
      print('❌ Error getting torque data: $e');
      return [];
    }
  }

  // Get latest torque data
  Future<TorqueValueData?> getLatestTorqueData() async {
    try {
      final results = await _dbService.query(
        'SELECT * FROM torgue_value_visulization ORDER BY created_at DESC LIMIT 1'
      );
      
      if (results == null || results.isEmpty) return null;

      final data = TorqueValueData.fromRow(results.first);
      print('✅ Retrieved latest torque data');
      return data;
    } catch (e) {
      print('❌ Error getting latest torque data: $e');
      return null;
    }
  }

  // Get torque data by VIN
  Future<List<TorqueValueData>> getTorqueDataByVin(String vinNumber) async {
    try {
      final results = await _dbService.query(
        'SELECT * FROM torgue_value_visulization WHERE vin_number = ? ORDER BY created_at DESC',
        [vinNumber]
      );
      
      if (results == null) return [];

      List<TorqueValueData> data = [];
      for (var row in results) {
        data.add(TorqueValueData.fromRow(row));
      }
      
      print('✅ Retrieved ${data.length} torque data records for VIN: $vinNumber');
      return data;
    } catch (e) {
      print('❌ Error getting torque data by VIN: $e');
      return [];
    }
  }

  // Update torque data
  Future<bool> updateTorqueData(int id, TorqueValueData data) async {
    try {
      final success = await _dbService.update(
        'torgue_value_visulization',
        data.toMap(),
        'id = ?',
        [id]
      );
      
      if (success) {
        print('✅ Torque data updated for ID: $id');
      }
      return success;
    } catch (e) {
      print('❌ Error updating torque data: $e');
      return false;
    }
  }

  // Delete torque data
  Future<bool> deleteTorqueData(int id) async {
    try {
      final success = await _dbService.delete(
        'torgue_value_visulization',
        'id = ?',
        [id]
      );
      
      if (success) {
        print('✅ Torque data deleted for ID: $id');
      }
      return success;
    } catch (e) {
      print('❌ Error deleting torque data: $e');
      return false;
    }
  }

  // Insert sample data
  Future<bool> insertSampleData() async {
    try {
      final sampleData = TorqueValueData.defaultData();
      final id = await insertTorqueData(sampleData);
      return id != null;
    } catch (e) {
      print('❌ Error inserting sample data: $e');
      return false;
    }
  }
}
