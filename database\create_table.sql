-- Create table for torque value visualization
-- This table stores all monitoring values from the monitor screen

CREATE TABLE IF NOT EXISTS `torgue_value_visulization` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  
  -- Vehicle Information
  `vehicle_model` VARCHAR(50) DEFAULT 'M6W',
  `seq_number` VARCHAR(10) DEFAULT '003',
  `vin_number` VARCHAR(50) DEFAULT 'LBEN2BKDBSZ050315',
  `post_line_vin` VARCHAR(50) DEFAULT 'LBECNAFD2SZ477474',
  
  -- AT 监测值 (Table 1 - Left table with cyan header)
  `at_monitor_range` VARCHAR(20) DEFAULT '0.9-1.4 kg/m',
  `at_monitor_1` DECIMAL(3,1) DEFAULT 1.3,
  `at_monitor_2` DECIMAL(3,1) DEFAULT 1.3,
  `at_monitor_3` DECIMAL(3,1) DEFAULT 1.2,
  `at_monitor_4` DECIMAL(3,1) DEFAULT 1.3,
  `at_status_1` ENUM('ok', 'ng') DEFAULT 'ok',
  `at_status_2` ENUM('ok', 'ng') DEFAULT 'ok',
  `at_status_3` ENUM('ok', 'ng') DEFAULT 'ok',
  `at_status_4` ENUM('ok', 'ng') DEFAULT 'ok',
  
  -- 当前监测值 (车身前) (Table 2 - Center table with purple header)
  `current_monitor_range` VARCHAR(20) DEFAULT '2.7-3.3 kg/m',
  `current_monitor_value` DECIMAL(3,1) DEFAULT 2.5,
  `current_monitor_status` ENUM('ok', 'ng') DEFAULT 'ng',
  
  -- 监测值 (Table 3 - Right table with orange header)
  `monitor_range` VARCHAR(20) DEFAULT '0.9-1.4 kg/m',
  `monitor_1` DECIMAL(3,1) DEFAULT 1.3,
  `monitor_2` DECIMAL(3,1) DEFAULT 1.3,
  `monitor_status_1` ENUM('ok', 'ng') DEFAULT 'ok',
  `monitor_status_2` ENUM('ok', 'ng') DEFAULT 'ok',
  
  -- 后轮监测结果计算结果 (Table 4 - Bottom table with pink header)
  `rear_wheel_range` VARCHAR(20) DEFAULT '0.7-1.1 kg/m',
  `rear_wheel_1` DECIMAL(3,1) DEFAULT 1.0,
  `rear_wheel_2` DECIMAL(3,1) DEFAULT 0.9,
  `rear_wheel_status_1` ENUM('ok', 'ng') DEFAULT 'ok',
  `rear_wheel_status_2` ENUM('ok', 'ng') DEFAULT 'ok',
  
  -- Inspection Items (检查项目)
  `inspection_status_1` ENUM('ok', 'ng') DEFAULT 'ok',
  `inspection_status_2` ENUM('ok', 'ng') DEFAULT 'ng',
  
  -- Progress (进度) - percentage from 0 to 100
  `progress_percentage` INT DEFAULT 60,
  
  -- Timestamps
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  -- Additional metadata
  `test_session_id` VARCHAR(50),
  `operator_name` VARCHAR(100),
  `test_location` VARCHAR(100),
  `notes` TEXT,
  
  -- Indexes for better performance
  INDEX `idx_created_at` (`created_at`),
  INDEX `idx_vin_number` (`vin_number`),
  INDEX `idx_test_session` (`test_session_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert sample data matching the current monitor screen values
INSERT INTO `torgue_value_visulization` (
  `vehicle_model`, `seq_number`, `vin_number`, `post_line_vin`,
  `at_monitor_range`, `at_monitor_1`, `at_monitor_2`, `at_monitor_3`, `at_monitor_4`,
  `at_status_1`, `at_status_2`, `at_status_3`, `at_status_4`,
  `current_monitor_range`, `current_monitor_value`, `current_monitor_status`,
  `monitor_range`, `monitor_1`, `monitor_2`, `monitor_status_1`, `monitor_status_2`,
  `rear_wheel_range`, `rear_wheel_1`, `rear_wheel_2`, `rear_wheel_status_1`, `rear_wheel_status_2`,
  `inspection_status_1`, `inspection_status_2`, `progress_percentage`,
  `test_session_id`, `operator_name`, `test_location`, `notes`
) VALUES (
  'M6W', '003', 'LBEN2BKDBSZ050315', 'LBECNAFD2SZ477474',
  '0.9-1.4 kg/m', 1.3, 1.3, 1.2, 1.3,
  'ok', 'ok', 'ok', 'ok',
  '2.7-3.3 kg/m', 2.5, 'ng',
  '0.9-1.4 kg/m', 1.3, 1.3, 'ok', 'ok',
  '0.7-1.1 kg/m', 1.0, 0.9, 'ok', 'ok',
  'ok', 'ng', 60,
  'SESSION_001', 'Test Operator', 'Production Line A', 'Initial test data from monitor screen'
);

-- Create a view for easier data retrieval
CREATE OR REPLACE VIEW `torque_monitoring_summary` AS
SELECT 
  id,
  vehicle_model,
  vin_number,
  -- AT monitoring summary
  CONCAT(at_monitor_1, '/', at_monitor_2, '/', at_monitor_3, '/', at_monitor_4) as at_values,
  CASE 
    WHEN at_status_1 = 'ok' AND at_status_2 = 'ok' AND at_status_3 = 'ok' AND at_status_4 = 'ok' 
    THEN 'ALL_OK' 
    ELSE 'HAS_ISSUES' 
  END as at_overall_status,
  -- Current monitoring
  current_monitor_value,
  current_monitor_status,
  -- Overall inspection status
  CASE 
    WHEN inspection_status_1 = 'ok' AND inspection_status_2 = 'ok' 
    THEN 'PASSED' 
    ELSE 'FAILED' 
  END as inspection_result,
  progress_percentage,
  created_at
FROM `torgue_value_visulization`
ORDER BY created_at DESC;
