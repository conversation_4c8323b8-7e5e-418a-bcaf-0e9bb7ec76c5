-- Create table for monitor screen data
-- This table stores ALL monitoring values from the monitor_screen.dart

CREATE TABLE IF NOT EXISTS `monitor_screen_data` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,

  -- ===========================================
  -- VEHICLE INFORMATION SECTION (Right Panel)
  -- ===========================================
  `vehicle_model` VARCHAR(50) NOT NULL DEFAULT 'M6W' COMMENT 'Vehicle model (车型)',
  `seq_number` VARCHAR(10) NOT NULL DEFAULT '003' COMMENT 'Sequence number (SEQ)',
  `vin_number` VARCHAR(50) NOT NULL DEFAULT 'LBEN2BKDBSZ050315' COMMENT 'VIN Number',
  `post_line_vin` VARCHAR(50) NOT NULL DEFAULT 'LBECNAFD2SZ477474' COMMENT 'Post-line VIN (下线后VIN No)',

  -- ===========================================
  -- TABLE 1: AT 监测值 (Left - Cyan Header)
  -- ===========================================
  `at_table_title` VARCHAR(50) DEFAULT 'AT 监测值' COMMENT 'Table 1 title',
  `at_range_spec` VARCHAR(30) DEFAULT '0.9-1.4 kg/m' COMMENT 'AT monitoring range specification',
  `at_value_1` DECIMAL(4,2) DEFAULT 1.30 COMMENT 'AT monitoring value #1',
  `at_value_2` DECIMAL(4,2) DEFAULT 1.30 COMMENT 'AT monitoring value #2',
  `at_value_3` DECIMAL(4,2) DEFAULT 1.20 COMMENT 'AT monitoring value #3',
  `at_value_4` DECIMAL(4,2) DEFAULT 1.30 COMMENT 'AT monitoring value #4',
  `at_status_1` ENUM('ok', 'ng') DEFAULT 'ok' COMMENT 'AT status indicator #1',
  `at_status_2` ENUM('ok', 'ng') DEFAULT 'ok' COMMENT 'AT status indicator #2',
  `at_status_3` ENUM('ok', 'ng') DEFAULT 'ok' COMMENT 'AT status indicator #3',
  `at_status_4` ENUM('ok', 'ng') DEFAULT 'ok' COMMENT 'AT status indicator #4',

  -- ===========================================
  -- TABLE 2: 当前监测值 (车身前) (Center - Purple Header)
  -- ===========================================
  `current_table_title` VARCHAR(50) DEFAULT '当前监测值 (车身前)' COMMENT 'Table 2 title',
  `current_range_spec` VARCHAR(30) DEFAULT '2.7-3.3 kg/m' COMMENT 'Current monitoring range specification',
  `current_value` DECIMAL(4,2) DEFAULT 2.50 COMMENT 'Current monitoring value',
  `current_status` ENUM('ok', 'ng') DEFAULT 'ng' COMMENT 'Current monitoring status',
  `current_sensor_id` VARCHAR(10) DEFAULT '#1' COMMENT 'Current sensor identifier',

  -- ===========================================
  -- TABLE 3: 监测值 (Right - Orange Header)
  -- ===========================================
  `monitor_table_title` VARCHAR(50) DEFAULT '监测值' COMMENT 'Table 3 title',
  `monitor_range_spec` VARCHAR(30) DEFAULT '0.9-1.4 kg/m' COMMENT 'Monitor range specification',
  `monitor_value_1` DECIMAL(4,2) DEFAULT 1.30 COMMENT 'Monitor value #1',
  `monitor_value_2` DECIMAL(4,2) DEFAULT 1.30 COMMENT 'Monitor value #2',
  `monitor_status_1` ENUM('ok', 'ng') DEFAULT 'ok' COMMENT 'Monitor status #1',
  `monitor_status_2` ENUM('ok', 'ng') DEFAULT 'ok' COMMENT 'Monitor status #2',
  `monitor_sensor_1` VARCHAR(10) DEFAULT '#1' COMMENT 'Monitor sensor #1 identifier',
  `monitor_sensor_2` VARCHAR(10) DEFAULT '#2' COMMENT 'Monitor sensor #2 identifier',

  -- ===========================================
  -- TABLE 4: 后轮监测结果计算结果 (Bottom - Pink Header)
  -- ===========================================
  `rear_wheel_table_title` VARCHAR(50) DEFAULT '后轮监测结果计算结果' COMMENT 'Table 4 title',
  `rear_wheel_range_spec` VARCHAR(30) DEFAULT '0.7-1.1 kg/m' COMMENT 'Rear wheel range specification',
  `rear_wheel_value_1` DECIMAL(4,2) DEFAULT 1.00 COMMENT 'Rear wheel value #1',
  `rear_wheel_value_2` DECIMAL(4,2) DEFAULT 0.90 COMMENT 'Rear wheel value #2',
  `rear_wheel_status_1` ENUM('ok', 'ng') DEFAULT 'ok' COMMENT 'Rear wheel status #1',
  `rear_wheel_status_2` ENUM('ok', 'ng') DEFAULT 'ok' COMMENT 'Rear wheel status #2',
  `rear_wheel_sensor_1` VARCHAR(10) DEFAULT '#1' COMMENT 'Rear wheel sensor #1 identifier',
  `rear_wheel_sensor_2` VARCHAR(10) DEFAULT '#2' COMMENT 'Rear wheel sensor #2 identifier',

  -- ===========================================
  -- INSPECTION ITEMS (检查项目) - Right Panel
  -- ===========================================
  `inspection_item_1_label` VARCHAR(20) DEFAULT 'OK' COMMENT 'Inspection item 1 label',
  `inspection_item_1_status` ENUM('ok', 'ng') DEFAULT 'ok' COMMENT 'Inspection item 1 status',
  `inspection_item_2_label` VARCHAR(20) DEFAULT 'NG' COMMENT 'Inspection item 2 label',
  `inspection_item_2_status` ENUM('ok', 'ng') DEFAULT 'ng' COMMENT 'Inspection item 2 status',

  -- ===========================================
  -- PROGRESS (进度) - Right Panel
  -- ===========================================
  `progress_label` VARCHAR(20) DEFAULT '进度' COMMENT 'Progress section label',
  `progress_percentage` INT DEFAULT 60 COMMENT 'Progress percentage (0-100)',
  `progress_completed` INT DEFAULT 60 COMMENT 'Completed portion',
  `progress_remaining` INT DEFAULT 40 COMMENT 'Remaining portion',

  -- ===========================================
  -- ADDITIONAL MONITORING DATA
  -- ===========================================
  `test_date` DATE DEFAULT (CURRENT_DATE) COMMENT 'Test execution date',
  `test_time` TIME DEFAULT (CURRENT_TIME) COMMENT 'Test execution time',
  `test_duration_seconds` INT DEFAULT 0 COMMENT 'Test duration in seconds',
  `test_result_overall` ENUM('pass', 'fail', 'pending') DEFAULT 'pending' COMMENT 'Overall test result',

  -- ===========================================
  -- METADATA AND TRACKING
  -- ===========================================
  `test_session_id` VARCHAR(50) COMMENT 'Unique test session identifier',
  `operator_name` VARCHAR(100) COMMENT 'Test operator name',
  `operator_id` VARCHAR(50) COMMENT 'Test operator ID',
  `test_location` VARCHAR(100) DEFAULT 'Production Line A' COMMENT 'Test location',
  `production_line` VARCHAR(50) DEFAULT 'Line A' COMMENT 'Production line identifier',
  `shift_number` VARCHAR(10) COMMENT 'Production shift number',
  `quality_inspector` VARCHAR(100) COMMENT 'Quality inspector name',
  `notes` TEXT COMMENT 'Additional notes and comments',
  `data_source` VARCHAR(50) DEFAULT 'monitor_screen_app' COMMENT 'Source of the data',

  -- ===========================================
  -- SYSTEM TIMESTAMPS
  -- ===========================================
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Record creation timestamp',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Record update timestamp',
  `deleted_at` TIMESTAMP NULL COMMENT 'Soft delete timestamp',

  -- ===========================================
  -- INDEXES FOR PERFORMANCE
  -- ===========================================
  INDEX `idx_created_at` (`created_at`),
  INDEX `idx_vin_number` (`vin_number`),
  INDEX `idx_test_session` (`test_session_id`),
  INDEX `idx_test_date` (`test_date`),
  INDEX `idx_operator` (`operator_name`),
  INDEX `idx_test_result` (`test_result_overall`),
  INDEX `idx_production_line` (`production_line`),

  -- ===========================================
  -- CONSTRAINTS
  -- ===========================================
  CONSTRAINT `chk_progress_percentage` CHECK (`progress_percentage` >= 0 AND `progress_percentage` <= 100),
  CONSTRAINT `chk_test_duration` CHECK (`test_duration_seconds` >= 0)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='Complete monitoring data from monitor_screen.dart - stores all table values, vehicle info, inspection items, and progress';

-- Insert sample data matching the current monitor screen values
INSERT INTO `monitor_screen_data` (
  -- Vehicle Information
  `vehicle_model`, `seq_number`, `vin_number`, `post_line_vin`,

  -- Table 1: AT 监测值
  `at_table_title`, `at_range_spec`,
  `at_value_1`, `at_value_2`, `at_value_3`, `at_value_4`,
  `at_status_1`, `at_status_2`, `at_status_3`, `at_status_4`,

  -- Table 2: 当前监测值 (车身前)
  `current_table_title`, `current_range_spec`, `current_value`, `current_status`, `current_sensor_id`,

  -- Table 3: 监测值
  `monitor_table_title`, `monitor_range_spec`,
  `monitor_value_1`, `monitor_value_2`, `monitor_status_1`, `monitor_status_2`,
  `monitor_sensor_1`, `monitor_sensor_2`,

  -- Table 4: 后轮监测结果计算结果
  `rear_wheel_table_title`, `rear_wheel_range_spec`,
  `rear_wheel_value_1`, `rear_wheel_value_2`, `rear_wheel_status_1`, `rear_wheel_status_2`,
  `rear_wheel_sensor_1`, `rear_wheel_sensor_2`,

  -- Inspection Items
  `inspection_item_1_label`, `inspection_item_1_status`,
  `inspection_item_2_label`, `inspection_item_2_status`,

  -- Progress
  `progress_label`, `progress_percentage`, `progress_completed`, `progress_remaining`,

  -- Metadata
  `test_session_id`, `operator_name`, `test_location`, `production_line`, `notes`
) VALUES (
  -- Vehicle Information
  'M6W', '003', 'LBEN2BKDBSZ050315', 'LBECNAFD2SZ477474',

  -- Table 1: AT 监测值 (Cyan header)
  'AT 监测值', '0.9-1.4 kg/m',
  1.30, 1.30, 1.20, 1.30,
  'ok', 'ok', 'ok', 'ok',

  -- Table 2: 当前监测值 (车身前) (Purple header)
  '当前监测值 (车身前)', '2.7-3.3 kg/m', 2.50, 'ng', '#1',

  -- Table 3: 监测值 (Orange header)
  '监测值', '0.9-1.4 kg/m',
  1.30, 1.30, 'ok', 'ok',
  '#1', '#2',

  -- Table 4: 后轮监测结果计算结果 (Pink header)
  '后轮监测结果计算结果', '0.7-1.1 kg/m',
  1.00, 0.90, 'ok', 'ok',
  '#1', '#2',

  -- Inspection Items
  'OK', 'ok', 'NG', 'ng',

  -- Progress (60% completed)
  '进度', 60, 60, 40,

  -- Metadata
  'SESSION_001', 'Test Operator', 'Production Line A', 'Line A', 'Sample data from monitor screen interface'
);

-- Create views for easier data analysis
CREATE OR REPLACE VIEW `monitor_summary` AS
SELECT
  id,
  vehicle_model,
  vin_number,
  seq_number,

  -- AT monitoring summary
  CONCAT(at_value_1, '/', at_value_2, '/', at_value_3, '/', at_value_4) as at_values,
  CASE
    WHEN at_status_1 = 'ok' AND at_status_2 = 'ok' AND at_status_3 = 'ok' AND at_status_4 = 'ok'
    THEN 'ALL_OK'
    ELSE 'HAS_ISSUES'
  END as at_overall_status,

  -- Current monitoring
  current_value,
  current_status,

  -- Monitor values
  CONCAT(monitor_value_1, '/', monitor_value_2) as monitor_values,
  CASE
    WHEN monitor_status_1 = 'ok' AND monitor_status_2 = 'ok'
    THEN 'ALL_OK'
    ELSE 'HAS_ISSUES'
  END as monitor_overall_status,

  -- Rear wheel values
  CONCAT(rear_wheel_value_1, '/', rear_wheel_value_2) as rear_wheel_values,
  CASE
    WHEN rear_wheel_status_1 = 'ok' AND rear_wheel_status_2 = 'ok'
    THEN 'ALL_OK'
    ELSE 'HAS_ISSUES'
  END as rear_wheel_overall_status,

  -- Overall inspection status
  CASE
    WHEN inspection_item_1_status = 'ok' AND inspection_item_2_status = 'ok'
    THEN 'PASSED'
    ELSE 'FAILED'
  END as inspection_result,

  progress_percentage,
  test_result_overall,
  operator_name,
  production_line,
  created_at
FROM `monitor_screen_data`
ORDER BY created_at DESC;

-- Create view for daily production summary
CREATE OR REPLACE VIEW `daily_production_summary` AS
SELECT
  DATE(created_at) as production_date,
  production_line,
  COUNT(*) as total_tests,
  SUM(CASE WHEN test_result_overall = 'pass' THEN 1 ELSE 0 END) as passed_tests,
  SUM(CASE WHEN test_result_overall = 'fail' THEN 1 ELSE 0 END) as failed_tests,
  SUM(CASE WHEN test_result_overall = 'pending' THEN 1 ELSE 0 END) as pending_tests,
  ROUND(AVG(progress_percentage), 2) as avg_progress,
  MIN(created_at) as first_test_time,
  MAX(created_at) as last_test_time
FROM `monitor_screen_data`
GROUP BY DATE(created_at), production_line
ORDER BY production_date DESC, production_line;
